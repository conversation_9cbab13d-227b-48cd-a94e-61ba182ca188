async function main() {
  console.log("DATACOIN start");
  const [deployer] = await ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)));

  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = await DATACOIN.deploy(deployer.address, {
    gasLimit: 2000000,
    gasPrice: ethers.parseUnits("20", "gwei")
  });

  await token.waitForDeployment();

  console.log("DATACOIN deployed to:", await token.getAddress());
  console.log("Transaction hash:", token.deploymentTransaction().hash);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
