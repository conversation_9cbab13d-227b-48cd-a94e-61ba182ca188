'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

interface WalletConnectProps {
  onWalletConnected: (provider: ethers.BrowserProvider, signer: ethers.JsonRpcSigner, address: string) => void;
  onWalletDisconnected: () => void;
}

export default function WalletConnect({ onWalletConnected, onWalletDisconnected }: WalletConnectProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [address, setAddress] = useState<string>('');
  const [chainId, setChainId] = useState<number>(0);
  const [balance, setBalance] = useState<string>('0');

  // Ki<PERSON>m tra kết nối khi component mount
  useEffect(() => {
    checkConnection();
    
    // Listen for account changes
    if (typeof window !== 'undefined' && window.ethereum) {
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);
    }

    return () => {
      if (typeof window !== 'undefined' && window.ethereum) {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
        window.ethereum.removeListener('chainChanged', handleChainChanged);
      }
    };
  }, []);

  const checkConnection = async () => {
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        const provider = new ethers.BrowserProvider(window.ethereum);
        const accounts = await provider.listAccounts();
        
        if (accounts.length > 0) {
          const signer = await provider.getSigner();
          const address = await signer.getAddress();
          const network = await provider.getNetwork();
          const balance = await provider.getBalance(address);
          
          setIsConnected(true);
          setAddress(address);
          setChainId(Number(network.chainId));
          setBalance(ethers.formatEther(balance));
          
          onWalletConnected(provider, signer, address);
        }
      } catch (error) {
        console.error('Error checking connection:', error);
      }
    }
  };

  const handleAccountsChanged = (accounts: string[]) => {
    if (accounts.length === 0) {
      disconnect();
    } else {
      checkConnection();
    }
  };

  const handleChainChanged = () => {
    checkConnection();
  };

  const connectWallet = async () => {
    if (typeof window === 'undefined' || !window.ethereum) {
      alert('MetaMask is not installed! Please install MetaMask to use this dApp.');
      return;
    }

    try {
      // Request account access
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      const network = await provider.getNetwork();
      const balance = await provider.getBalance(address);

      setIsConnected(true);
      setAddress(address);
      setChainId(Number(network.chainId));
      setBalance(ethers.formatEther(balance));

      onWalletConnected(provider, signer, address);

      // Check if we're on the correct network (Ethermint - Chain ID 9000)
      if (Number(network.chainId) !== 9000) {
        await switchToEthermint();
      }

    } catch (error) {
      console.error('Error connecting wallet:', error);
      alert('Failed to connect wallet. Please try again.');
    }
  };

  const switchToEthermint = async () => {
    if (typeof window === 'undefined' || !window.ethereum) return;

    try {
      // Try to switch to Ethermint network
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: '0x2328' }], // 9000 in hex
      });
    } catch (switchError: any) {
      // If network doesn't exist, add it
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [
              {
                chainId: '0x2328', // 9000 in hex
                chainName: 'Ethermint Local',
                nativeCurrency: {
                  name: 'ETH',
                  symbol: 'ETH',
                  decimals: 18,
                },
                rpcUrls: ['http://127.0.0.1:8545'],
                blockExplorerUrls: ['http://localhost:8000'],
              },
            ],
          });
        } catch (addError) {
          console.error('Error adding network:', addError);
          alert('Failed to add Ethermint network to MetaMask');
        }
      } else {
        console.error('Error switching network:', switchError);
      }
    }
  };

  const disconnect = () => {
    setIsConnected(false);
    setAddress('');
    setChainId(0);
    setBalance('0');
    onWalletDisconnected();
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
      <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
        Wallet Connection
      </h2>
      
      {!isConnected ? (
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Connect your MetaMask wallet to interact with DTC token
          </p>
          <button
            onClick={connectWallet}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors"
          >
            Connect MetaMask
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-green-500 font-semibold">✅ Connected</span>
            <button
              onClick={disconnect}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm transition-colors"
            >
              Disconnect
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
              <p className="text-sm text-gray-600 dark:text-gray-300">Address:</p>
              <p className="font-mono text-sm font-semibold text-gray-800 dark:text-white">
                {formatAddress(address)}
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
              <p className="text-sm text-gray-600 dark:text-gray-300">ETH Balance:</p>
              <p className="font-mono text-sm font-semibold text-gray-800 dark:text-white">
                {parseFloat(balance).toFixed(4)} ETH
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
              <p className="text-sm text-gray-600 dark:text-gray-300">Chain ID:</p>
              <p className="font-mono text-sm font-semibold text-gray-800 dark:text-white">
                {chainId} {chainId === 9000 ? '(Ethermint)' : '(Wrong Network)'}
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
              <p className="text-sm text-gray-600 dark:text-gray-300">Network:</p>
              {chainId === 9000 ? (
                <p className="text-green-600 font-semibold text-sm">✅ Ethermint</p>
              ) : (
                <div>
                  <p className="text-red-600 font-semibold text-sm">❌ Wrong Network</p>
                  <button
                    onClick={switchToEthermint}
                    className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-xs mt-1 transition-colors"
                  >
                    Switch to Ethermint
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
