'use client';

import { useState } from 'react';
import { ethers } from 'ethers';

const DTC_ABI = [
  "function transfer(address to, uint256 amount) returns (bool)",
  "function balanceOf(address) view returns (uint256)"
];

const DTC_CONTRACT_ADDRESS = "******************************************";

interface TokenTransferProps {
  provider: ethers.BrowserProvider | null;
  signer: ethers.JsonRpcSigner | null;
  userAddress: string;
  onTransferComplete: () => void;
}

export default function TokenTransfer({ provider, signer, userAddress, onTransferComplete }: TokenTransferProps) {
  const [recipient, setRecipient] = useState('');
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [txHash, setTxHash] = useState('');
  const [error, setError] = useState('');

  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!signer || !provider) {
      setError('Please connect your wallet first');
      return;
    }

    if (!recipient || !amount) {
      setError('Please fill in all fields');
      return;
    }

    if (!ethers.isAddress(recipient)) {
      setError('Invalid recipient address');
      return;
    }

    if (parseFloat(amount) <= 0) {
      setError('Amount must be greater than 0');
      return;
    }

    setLoading(true);
    setError('');
    setTxHash('');

    try {
      const contract = new ethers.Contract(DTC_CONTRACT_ADDRESS, DTC_ABI, signer);
      
      // Check user balance first
      const balance = await contract.balanceOf(userAddress);
      const transferAmount = ethers.parseEther(amount);
      
      if (balance < transferAmount) {
        throw new Error('Insufficient DTC balance');
      }

      // Execute transfer
      const tx = await contract.transfer(recipient, transferAmount);
      setTxHash(tx.hash);
      
      // Wait for confirmation
      await tx.wait();
      
      // Reset form
      setRecipient('');
      setAmount('');
      
      // Notify parent component
      onTransferComplete();
      
      alert('Transfer successful!');

    } catch (err: any) {
      console.error('Transfer error:', err);
      setError(err.message || 'Transfer failed');
    } finally {
      setLoading(false);
    }
  };

  const quickFillAddresses = [
    { name: 'Test Address 1', address: '******************************************' },
    { name: 'Test Address 2', address: '******************************************' },
    { name: 'Test Address 3', address: '******************************************' }
  ];

  if (!provider || !signer || !userAddress) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
          Transfer DTC Tokens
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Please connect your wallet to transfer tokens
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
      <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
        Transfer DTC Tokens
      </h2>

      <form onSubmit={handleTransfer} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Recipient Address
          </label>
          <input
            type="text"
            value={recipient}
            onChange={(e) => setRecipient(e.target.value)}
            placeholder="0x..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            disabled={loading}
          />
          
          {/* Quick fill buttons */}
          <div className="mt-2 flex flex-wrap gap-2">
            {quickFillAddresses.map((addr, index) => (
              <button
                key={index}
                type="button"
                onClick={() => setRecipient(addr.address)}
                className="text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 px-2 py-1 rounded transition-colors"
                disabled={loading}
              >
                {addr.name}
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Amount (DTC)
          </label>
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="0.0"
            step="0.000000000000000001"
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            disabled={loading}
          />
          
          {/* Quick amount buttons */}
          <div className="mt-2 flex flex-wrap gap-2">
            {['1', '10', '100', '1000'].map((amt) => (
              <button
                key={amt}
                type="button"
                onClick={() => setAmount(amt)}
                className="text-xs bg-blue-200 hover:bg-blue-300 dark:bg-blue-600 dark:hover:bg-blue-500 px-2 py-1 rounded transition-colors"
                disabled={loading}
              >
                {amt} DTC
              </button>
            ))}
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {txHash && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <p className="font-semibold">Transaction submitted!</p>
            <p className="text-sm">Hash: {txHash}</p>
            <a
              href={`http://localhost:8000/tx/${txHash}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline text-sm"
            >
              View on Explorer →
            </a>
          </div>
        )}

        <button
          type="submit"
          disabled={loading || !recipient || !amount}
          className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-bold py-3 px-6 rounded-lg transition-colors"
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Processing...
            </div>
          ) : (
            'Transfer DTC'
          )}
        </button>
      </form>

      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h3 className="font-semibold text-gray-800 dark:text-white mb-2">💡 Tips:</h3>
        <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
          <li>• Make sure you have enough ETH for gas fees</li>
          <li>• Double-check the recipient address before sending</li>
          <li>• Transactions are irreversible once confirmed</li>
          <li>• You can view all transactions on the explorer</li>
        </ul>
      </div>
    </div>
  );
}
