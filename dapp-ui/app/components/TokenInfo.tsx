'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// DTC Token ABI (chỉ các functions cần thiết)
const DTC_ABI = [
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function decimals() view returns (uint8)",
  "function totalSupply() view returns (uint256)",
  "function balanceOf(address) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function approve(address spender, uint256 amount) returns (bool)",
  "function allowance(address owner, address spender) view returns (uint256)"
];

const DTC_CONTRACT_ADDRESS = "******************************************";

interface TokenInfoProps {
  provider: ethers.BrowserProvider | null;
  signer: ethers.JsonRpcSigner | null;
  userAddress: string;
}

export default function TokenInfo({ provider, signer, userAddress }: TokenInfoProps) {
  const [tokenInfo, setTokenInfo] = useState({
    name: '',
    symbol: '',
    decimals: 0,
    totalSupply: '0',
    userBalance: '0'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (provider && userAddress) {
      loadTokenInfo();
    }
  }, [provider, userAddress]);

  const loadTokenInfo = async () => {
    if (!provider) return;

    setLoading(true);
    setError('');

    try {
      const contract = new ethers.Contract(DTC_CONTRACT_ADDRESS, DTC_ABI, provider);

      const [name, symbol, decimals, totalSupply, userBalance] = await Promise.all([
        contract.name(),
        contract.symbol(),
        contract.decimals(),
        contract.totalSupply(),
        contract.balanceOf(userAddress)
      ]);

      setTokenInfo({
        name,
        symbol,
        decimals: Number(decimals),
        totalSupply: ethers.formatEther(totalSupply),
        userBalance: ethers.formatEther(userBalance)
      });

    } catch (err: any) {
      console.error('Error loading token info:', err);
      setError('Failed to load token information');
    } finally {
      setLoading(false);
    }
  };

  const addTokenToMetaMask = async () => {
    if (typeof window === 'undefined' || !window.ethereum) {
      alert('MetaMask is not available');
      return;
    }

    try {
      await window.ethereum.request({
        method: 'wallet_watchAsset',
        params: {
          type: 'ERC20',
          options: {
            address: DTC_CONTRACT_ADDRESS,
            symbol: tokenInfo.symbol,
            decimals: tokenInfo.decimals,
            image: '', // You can add a token logo URL here
          },
        },
      });
      alert('DTC token added to MetaMask!');
    } catch (error) {
      console.error('Error adding token to MetaMask:', error);
      alert('Failed to add token to MetaMask');
    }
  };

  if (!provider || !userAddress) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
          DTC Token Information
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Please connect your wallet to view token information
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
          DTC Token Information
        </h2>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-300">Loading token info...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
          DTC Token Information
        </h2>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
          <button
            onClick={loadTokenInfo}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          DTC Token Information
        </h2>
        <button
          onClick={loadTokenInfo}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm transition-colors"
        >
          Refresh
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Token Name</h3>
          <p className="text-2xl font-bold">{tokenInfo.name}</p>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-teal-600 text-white p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Symbol</h3>
          <p className="text-2xl font-bold">{tokenInfo.symbol}</p>
        </div>

        <div className="bg-gradient-to-r from-orange-500 to-red-600 text-white p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Decimals</h3>
          <p className="text-2xl font-bold">{tokenInfo.decimals}</p>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Total Supply</h3>
          <p className="text-xl font-bold">{parseFloat(tokenInfo.totalSupply).toLocaleString()} {tokenInfo.symbol}</p>
        </div>

        <div className="bg-gradient-to-r from-indigo-500 to-blue-600 text-white p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Your Balance</h3>
          <p className="text-xl font-bold">{parseFloat(tokenInfo.userBalance).toLocaleString()} {tokenInfo.symbol}</p>
        </div>

        <div className="bg-gradient-to-r from-gray-500 to-gray-700 text-white p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Contract</h3>
          <p className="text-sm font-mono break-all">{DTC_CONTRACT_ADDRESS}</p>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <button
          onClick={addTokenToMetaMask}
          className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
        >
          <span className="mr-2">🦊</span>
          Add DTC to MetaMask
        </button>

        <a
          href={`http://localhost:8000/address/${DTC_CONTRACT_ADDRESS}`}
          target="_blank"
          rel="noopener noreferrer"
          className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
        >
          <span className="mr-2">🔍</span>
          View on Explorer
        </a>

        <a
          href={`http://localhost:8000/address/${userAddress}`}
          target="_blank"
          rel="noopener noreferrer"
          className="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
        >
          <span className="mr-2">👤</span>
          View Your Address
        </a>
      </div>
    </div>
  );
}
