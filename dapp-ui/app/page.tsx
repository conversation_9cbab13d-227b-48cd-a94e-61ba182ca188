'use client';

import { useState } from 'react';
import { ethers } from 'ethers';
import WalletConnect from './components/WalletConnect';
import TokenInfo from './components/TokenInfo';
import TokenTransfer from './components/TokenTransfer';

export default function Home() {
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | null>(null);
  const [userAddress, setUserAddress] = useState<string>('');
  const [refreshKey, setRefreshKey] = useState(0);

  const handleWalletConnected = (
    newProvider: ethers.BrowserProvider,
    newSigner: ethers.JsonRpcSigner,
    address: string
  ) => {
    setProvider(newProvider);
    setSigner(newSigner);
    setUserAddress(address);
  };

  const handleWalletDisconnected = () => {
    setProvider(null);
    setSigner(null);
    setUserAddress('');
  };

  const handleTransferComplete = () => {
    // Trigger refresh of token info
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                🪙 DTC Token dApp
              </h1>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Ethermint Network
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Wallet Connection */}
        <WalletConnect
          onWalletConnected={handleWalletConnected}
          onWalletDisconnected={handleWalletDisconnected}
        />

        {/* Token Information */}
        <TokenInfo
          key={refreshKey}
          provider={provider}
          signer={signer}
          userAddress={userAddress}
        />

        {/* Token Transfer */}
        <TokenTransfer
          provider={provider}
          signer={signer}
          userAddress={userAddress}
          onTransferComplete={handleTransferComplete}
        />

        {/* Footer Info */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
            📋 dApp Information
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                Network Details
              </h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li><strong>Network:</strong> Ethermint Local</li>
                <li><strong>Chain ID:</strong> 9000</li>
                <li><strong>RPC URL:</strong> http://127.0.0.1:8545</li>
                <li><strong>Explorer:</strong> http://localhost:8000</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
                DTC Token Details
              </h3>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li><strong>Contract:</strong> ******************************************</li>
                <li><strong>Symbol:</strong> DTC</li>
                <li><strong>Decimals:</strong> 18</li>
                <li><strong>Type:</strong> ERC-20</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              🚀 Getting Started
            </h3>
            <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
              <li>Install MetaMask browser extension</li>
              <li>Click "Connect MetaMask" to connect your wallet</li>
              <li>Add Ethermint network if prompted</li>
              <li>Add DTC token to MetaMask to see your balance</li>
              <li>Start transferring DTC tokens!</li>
            </ol>
          </div>
        </div>
      </main>
    </div>
  );
}
