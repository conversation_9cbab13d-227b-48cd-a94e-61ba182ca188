'use client';

import { useState } from 'react';
import { ethers } from 'ethers';

const DTC_CONTRACT = "******************************************";
const DTC_ABI = [
  "function balanceOf(address) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)"
];

export default function Home() {
  const [account, setAccount] = useState('');
  const [balance, setBalance] = useState('0');
  const [recipient, setRecipient] = useState('');
  const [amount, setAmount] = useState('');

  const connectWallet = async () => {
    if (!window.ethereum) return alert('Install MetaMask!');

    try {
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();

      setAccount(address);

      // Add Ethermint network
      try {
        await window.ethereum.request({
          method: 'wallet_addEthereumChain',
          params: [{
            chainId: '0x2328',
            chainName: 'Ethermint',
            rpcUrls: ['http://127.0.0.1:8545'],
            nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 }
          }]
        });
      } catch (e) {}

      // Get DTC balance
      const contract = new ethers.Contract(DTC_CONTRACT, DTC_ABI, provider);
      const bal = await contract.balanceOf(address);
      setBalance(ethers.formatEther(bal));

    } catch (error) {
      console.error(error);
    }
  };

  const addToken = async () => {
    await window.ethereum.request({
      method: 'wallet_watchAsset',
      params: {
        type: 'ERC20',
        options: {
          address: DTC_CONTRACT,
          symbol: 'DTC',
          decimals: 18
        }
      }
    });
  };

  const transfer = async () => {
    if (!account || !recipient || !amount) return;

    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(DTC_CONTRACT, DTC_ABI, signer);

      const tx = await contract.transfer(recipient, ethers.parseEther(amount));
      await tx.wait();

      alert('Transfer successful!');
      setRecipient('');
      setAmount('');

      // Refresh balance
      const bal = await contract.balanceOf(account);
      setBalance(ethers.formatEther(bal));

    } catch (error) {
      alert('Transfer failed: ' + error.message);
    }
  };

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">🪙 DTC Token dApp</h1>

      {!account ? (
        <button
          onClick={connectWallet}
          className="w-full bg-blue-500 text-white p-3 rounded mb-4"
        >
          Connect MetaMask
        </button>
      ) : (
        <div className="space-y-4">
          <div className="p-4 bg-gray-100 rounded">
            <p><strong>Account:</strong> {account.slice(0,6)}...{account.slice(-4)}</p>
            <p><strong>DTC Balance:</strong> {parseFloat(balance).toFixed(2)}</p>
          </div>

          <button
            onClick={addToken}
            className="w-full bg-orange-500 text-white p-2 rounded"
          >
            Add DTC to MetaMask
          </button>

          <div className="space-y-2">
            <input
              placeholder="Recipient address"
              value={recipient}
              onChange={(e) => setRecipient(e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              placeholder="Amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="w-full p-2 border rounded"
            />
            <button
              onClick={transfer}
              className="w-full bg-green-500 text-white p-2 rounded"
            >
              Transfer DTC
            </button>
          </div>

          <div className="text-xs text-gray-600">
            <p>Contract: {DTC_CONTRACT}</p>
            <p>Network: Ethermint (Chain ID: 9000)</p>
          </div>
        </div>
      )}
    </div>
  );
}
