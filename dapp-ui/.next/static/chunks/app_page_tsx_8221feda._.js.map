{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ethers } from 'ethers';\n\nconst DTC_CONTRACT = \"******************************************\";\nconst DTC_ABI = [\n  \"function balanceOf(address) view returns (uint256)\",\n  \"function transfer(address to, uint256 amount) returns (bool)\"\n];\n\nexport default function Home() {\n  const [account, setAccount] = useState('');\n  const [balance, setBalance] = useState('0');\n  const [recipient, setRecipient] = useState('');\n  const [amount, setAmount] = useState('');\n\n  const connectWallet = async () => {\n    if (!window.ethereum) return alert('Install MetaMask!');\n\n    try {\n      await window.ethereum.request({ method: 'eth_requestAccounts' });\n      const provider = new ethers.BrowserProvider(window.ethereum);\n      const signer = await provider.getSigner();\n      const address = await signer.getAddress();\n\n      setAccount(address);\n\n      // Add Ethermint network\n      try {\n        await window.ethereum.request({\n          method: 'wallet_addEthereumChain',\n          params: [{\n            chainId: '0x2328',\n            chainName: 'Ethermint',\n            rpcUrls: ['http://127.0.0.1:8545'],\n            nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 }\n          }]\n        });\n      } catch (e) {}\n\n      // Get DTC balance\n      const contract = new ethers.Contract(DTC_CONTRACT, DTC_ABI, provider);\n      const bal = await contract.balanceOf(address);\n      setBalance(ethers.formatEther(bal));\n\n    } catch (error) {\n      console.error(error);\n    }\n  };\n\n  const addToken = async () => {\n    await window.ethereum.request({\n      method: 'wallet_watchAsset',\n      params: {\n        type: 'ERC20',\n        options: {\n          address: DTC_CONTRACT,\n          symbol: 'DTC',\n          decimals: 18\n        }\n      }\n    });\n  };\n\n  const transfer = async () => {\n    if (!account || !recipient || !amount) return;\n\n    try {\n      const provider = new ethers.BrowserProvider(window.ethereum);\n      const signer = await provider.getSigner();\n      const contract = new ethers.Contract(DTC_CONTRACT, DTC_ABI, signer);\n\n      const tx = await contract.transfer(recipient, ethers.parseEther(amount));\n      await tx.wait();\n\n      alert('Transfer successful!');\n      setRecipient('');\n      setAmount('');\n\n      // Refresh balance\n      const bal = await contract.balanceOf(account);\n      setBalance(ethers.formatEther(bal));\n\n    } catch (error) {\n      alert('Transfer failed: ' + error.message);\n    }\n  };\n\n  return (\n    <div className=\"p-8 max-w-md mx-auto\">\n      <h1 className=\"text-2xl font-bold mb-6\">🪙 DTC Token dApp</h1>\n\n      {!account ? (\n        <button\n          onClick={connectWallet}\n          className=\"w-full bg-blue-500 text-white p-3 rounded mb-4\"\n        >\n          Connect MetaMask\n        </button>\n      ) : (\n        <div className=\"space-y-4\">\n          <div className=\"p-4 bg-gray-100 rounded\">\n            <p><strong>Account:</strong> {account.slice(0,6)}...{account.slice(-4)}</p>\n            <p><strong>DTC Balance:</strong> {parseFloat(balance).toFixed(2)}</p>\n          </div>\n\n          <button\n            onClick={addToken}\n            className=\"w-full bg-orange-500 text-white p-2 rounded\"\n          >\n            Add DTC to MetaMask\n          </button>\n\n          <div className=\"space-y-2\">\n            <input\n              placeholder=\"Recipient address\"\n              value={recipient}\n              onChange={(e) => setRecipient(e.target.value)}\n              className=\"w-full p-2 border rounded\"\n            />\n            <input\n              placeholder=\"Amount\"\n              value={amount}\n              onChange={(e) => setAmount(e.target.value)}\n              className=\"w-full p-2 border rounded\"\n            />\n            <button\n              onClick={transfer}\n              className=\"w-full bg-green-500 text-white p-2 rounded\"\n            >\n              Transfer DTC\n            </button>\n          </div>\n\n          <div className=\"text-xs text-gray-600\">\n            <p>Contract: {DTC_CONTRACT}</p>\n            <p>Network: Ethermint (Chain ID: 9000)</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,eAAe;AACrB,MAAM,UAAU;IACd;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,gBAAgB;QACpB,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO,MAAM;QAEnC,IAAI;YACF,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;gBAAE,QAAQ;YAAsB;YAC9D,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,eAAe,CAAC,OAAO,QAAQ;YAC3D,MAAM,SAAS,MAAM,SAAS,SAAS;YACvC,MAAM,UAAU,MAAM,OAAO,UAAU;YAEvC,WAAW;YAEX,wBAAwB;YACxB,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;oBAC5B,QAAQ;oBACR,QAAQ;wBAAC;4BACP,SAAS;4BACT,WAAW;4BACX,SAAS;gCAAC;6BAAwB;4BAClC,gBAAgB;gCAAE,MAAM;gCAAO,QAAQ;gCAAO,UAAU;4BAAG;wBAC7D;qBAAE;gBACJ;YACF,EAAE,OAAO,GAAG,CAAC;YAEb,kBAAkB;YAClB,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,cAAc,SAAS;YAC5D,MAAM,MAAM,MAAM,SAAS,SAAS,CAAC;YACrC,WAAW,mLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAEhC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,WAAW;QACf,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;YAC5B,QAAQ;YACR,QAAQ;gBACN,MAAM;gBACN,SAAS;oBACP,SAAS;oBACT,QAAQ;oBACR,UAAU;gBACZ;YACF;QACF;IACF;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ;QAEvC,IAAI;YACF,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,eAAe,CAAC,OAAO,QAAQ;YAC3D,MAAM,SAAS,MAAM,SAAS,SAAS;YACvC,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,cAAc,SAAS;YAE5D,MAAM,KAAK,MAAM,SAAS,QAAQ,CAAC,WAAW,mLAAA,CAAA,SAAM,CAAC,UAAU,CAAC;YAChE,MAAM,GAAG,IAAI;YAEb,MAAM;YACN,aAAa;YACb,UAAU;YAEV,kBAAkB;YAClB,MAAM,MAAM,MAAM,SAAS,SAAS,CAAC;YACrC,WAAW,mLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAEhC,EAAE,OAAO,OAAO;YACd,MAAM,sBAAsB,MAAM,OAAO;QAC3C;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;YAEvC,CAAC,wBACA,6LAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;qCAID,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAiB;oCAAE,QAAQ,KAAK,CAAC,GAAE;oCAAG;oCAAI,QAAQ,KAAK,CAAC,CAAC;;;;;;;0CACpE,6LAAC;;kDAAE,6LAAC;kDAAO;;;;;;oCAAqB;oCAAE,WAAW,SAAS,OAAO,CAAC;;;;;;;;;;;;;kCAGhE,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;;;;;;0CAEZ,6LAAC;gCACC,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;;;;;0CAEZ,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAE;oCAAW;;;;;;;0CACd,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GApIwB;KAAA", "debugId": null}}]}