{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/_assert.js", "sourceRoot": "", "sources": ["../src/_assert.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;AAAA,SAAS,MAAM,CAAC,CAAS;IACvB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,CAAA,wBAAA,EAA2B,CAAC,EAAE,CAAC,CAAC;AACzF,CAAC;AAED,SAAS,IAAI,CAAC,CAAU;IACtB,IAAI,OAAO,CAAC,KAAK,SAAS,EAAE,MAAM,IAAI,KAAK,CAAC,CAAA,sBAAA,EAAyB,CAAC,EAAE,CAAC,CAAC;AAC5E,CAAC;AAED,SAAS,KAAK,CAAC,CAAyB,EAAE,GAAG,OAAiB;IAC5D,IAAI,CAAC,CAAC,CAAC,YAAY,UAAU,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACvE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,EACnD,MAAM,IAAI,KAAK,CAAC,CAAA,8BAAA,EAAiC,OAAO,CAAA,gBAAA,EAAmB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AAC3F,CAAC;AAQD,SAAS,IAAI,CAAC,IAAU;IACtB,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,EACjE,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;IACrE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,MAAM,CAAC,QAAa,EAAE,aAAa,GAAG,IAAI;IACjD,IAAI,QAAQ,CAAC,SAAS,EAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IAC5E,IAAI,aAAa,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACnG,CAAC;AACD,SAAS,MAAM,CAAC,GAAQ,EAAE,QAAa;IACrC,KAAK,CAAC,GAAG,CAAC,CAAC;IACX,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;IAC/B,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;QACpB,MAAM,IAAI,KAAK,CAAC,CAAA,sDAAA,EAAyD,GAAG,EAAE,CAAC,CAAC;KACjF;AACH,CAAC;;AAID,MAAM,MAAM,GAAG;IAAE,MAAM;IAAE,IAAI;IAAE,KAAK;IAAE,IAAI;IAAE,MAAM;IAAE,MAAM;AAAA,CAAE,CAAC;uCAC9C,MAAM,CAAC", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/_u64.js", "sourceRoot": "", "sources": ["../src/_u64.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AACvD,MAAM,IAAI,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAExC,+EAA+E;AAC/E,SAAS,OAAO,CAAC,CAAS,EAAE,EAAE,GAAG,KAAK;IACpC,IAAI,EAAE,EAAE,OAAO;QAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC;QAAE,CAAC,EAAE,MAAM,CAAE,AAAD,CAAE,IAAI,IAAI,CAAC,EAAG,UAAU,CAAC;IAAA,CAAE,CAAC;IAClF,OAAO;QAAE,CAAC,EAAE,MAAM,CAAC,AAAC,CAAC,IAAI,IAAI,CAAC,EAAG,UAAU,CAAC,GAAG,CAAC;QAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;IAAA,CAAE,CAAC;AACpF,CAAC;AAED,SAAS,KAAK,CAAC,GAAa,EAAE,EAAE,GAAG,KAAK;IACtC,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACnC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG;YAAC,CAAC;YAAE,CAAC;SAAC,CAAC;KACzB;IACD,OAAO;QAAC,EAAE;QAAE,EAAE;KAAC,CAAC;AAClB,CAAC;AAED,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAI,AAAD,CAAD,KAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,EAAG,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACpF,uBAAuB;AACvB,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,EAAU,EAAE,CAAS,EAAE,CAAG,CAAD,AAAE,KAAK,CAAC,CAAC;AAC5D,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAF,AAAG,IAAI,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAI,CAAC,CAAF,IAAO,CAAC,CAAC,CAAC;AAC/E,oCAAoC;AACpC,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAF,AAAG,KAAK,CAAC,CAAC,EAAI,CAAC,AAAF,IAAM,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAChF,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAC,AAAH,IAAO,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAI,CAAC,CAAF,IAAO,CAAC,CAAC,CAAC;AAChF,gEAAgE;AAChE,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAF,AAAG,IAAI,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAI,CAAC,CAAF,IAAO,AAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACvF,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAF,AAAG,KAAK,AAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAI,CAAC,CAAF,GAAM,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,+CAA+C;AAC/C,MAAM,OAAO,GAAG,CAAC,EAAU,EAAE,CAAS,EAAE,CAAG,CAAD,AAAE,CAAC;AAC7C,MAAM,OAAO,GAAG,CAAC,CAAS,EAAE,EAAU,EAAE,CAAG,CAAD,AAAE,CAAC;AAC7C,mCAAmC;AACnC,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAC,AAAH,IAAO,CAAC,CAAC,EAAI,CAAD,AAAE,KAAK,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAChF,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAI,AAAD,CAAE,AAAH,IAAO,CAAC,CAAC,EAAI,CAAD,AAAE,KAAK,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAChF,+DAA+D;AAC/D,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAF,AAAG,IAAI,AAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAI,CAAC,CAAF,IAAQ,AAAD,EAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAI,AAAD,CAAD,AAAG,IAAK,AAAD,CAAE,GAAG,EAAE,CAAC,CAAC,CAAI,CAAC,CAAF,IAAO,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAEvF,8EAA8E;AAC9E,0EAA0E;AAC1E,SAAS,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;IACzD,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAClC,OAAO;QAAE,CAAC,EAAE,AAAC,EAAE,GAAG,EAAE,GAAG,CAAC,AAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAG,CAAC,CAAC,CAAC,EAAG,CAAC;QAAE,CAAC,EAAE,CAAC,GAAG,CAAC;IAAA,CAAE,CAAC;AAC9D,CAAC;AACD,qCAAqC;AACrC,MAAM,KAAK,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,CAAG,CAAD,AAAE,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3F,MAAM,KAAK,GAAG,CAAC,GAAW,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,CAC9D,AAAC,CAD+D,CAC7D,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,AAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAG,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;AAC7C,MAAM,KAAK,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,CAC7D,CAD+D,AAC9D,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACpD,MAAM,KAAK,GAAG,CAAC,GAAW,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,CAC1E,AAAC,CAD2E,CACzE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,AAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAG,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;AAClD,MAAM,KAAK,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,CACzE,CAD2E,AAC1E,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjE,MAAM,KAAK,GAAG,CAAC,GAAW,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,CACtF,AAAC,CADuF,CACrF,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,AAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAG,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;;AAWvD,kBAAkB;AAClB,MAAM,GAAG,GAAG;IACV,OAAO;IAAE,KAAK;IAAE,KAAK;IACrB,KAAK;IAAE,KAAK;IACZ,MAAM;IAAE,MAAM;IAAE,MAAM;IAAE,MAAM;IAC9B,OAAO;IAAE,OAAO;IAChB,MAAM;IAAE,MAAM;IAAE,MAAM;IAAE,MAAM;IAC9B,GAAG;IAAE,KAAK;IAAE,KAAK;IAAE,KAAK;IAAE,KAAK;IAAE,KAAK;IAAE,KAAK;CAC9C,CAAC;uCACa,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/cryptoNode.js", "sourceRoot": "", "sources": ["../src/cryptoNode.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,oFAAoF;AACpF,4BAA4B;AAC5B,iDAAiD;AACjD,aAAa;;;;AACb,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;;AAC3B,MAAM,MAAM,GACjB,EAAE,uHAAI,OAAO,EAAE,wHAAK,QAAQ,IAAI,WAAW,IAAI,EAAE,CAAC,CAAC,oHAAE,EAAE,oHAAC,SAAiB,CAAC,CAAC,CAAC,SAAS,CAAC", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/utils.js", "sourceRoot": "", "sources": ["../src/utils.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,oEAAA,EAAsE,CAEtE,oFAAoF;AACpF,sEAAsE;AACtE,kEAAkE;AAClE,8DAA8D;AAC9D,+DAA+D;AAC/D,8DAA8D;;;;;;;;;;;;;;;;;;;;;AAC9D,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;;AAM9C,MAAM,GAAG,GAAG,CAAC,CAAM,EAAmB,CAAG,CAAD,AAAE,YAAY,UAAU,CAAC;AAE1D,MAAM,EAAE,GAAG,CAAC,GAAe,EAAE,CAAG,CAAD,GAAK,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAC3F,MAAM,GAAG,GAAG,CAAC,GAAe,EAAE,CACnC,CADqC,GACjC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AAGvE,MAAM,UAAU,GAAG,CAAC,GAAe,EAAE,CAC1C,CAD4C,GACxC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAGpD,MAAM,IAAI,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,CAAG,AAAC,CAAF,GAAM,IAAK,AAAD,EAAG,GAAG,KAAK,CAAC,CAAC,CAAI,EAAD,EAAK,KAAK,KAAK,CAAC,CAAC;AAIxF,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC;IAAC,UAAU;CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACrF,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAE1E,MAAM,KAAK,GAAG,aAAA,EAAe,CAAC,KAAK,CAAC,IAAI,CAAC;IAAE,MAAM,EAAE,GAAG;AAAA,CAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAC/D,CADiE,AAChE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAChC,CAAC;AAII,SAAU,UAAU,CAAC,KAAiB;IAC1C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACxD,oCAAoC;IACpC,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACrC,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACxB;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAKK,SAAU,UAAU,CAAC,GAAW;IACpC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,OAAO,GAAG,CAAC,CAAC;IACvF,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,yDAAyD,GAAG,GAAG,CAAC,CAAC;IAC9F,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACtC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACrC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACpC,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7E,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACjB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAKM,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE,AAAE,CAAC,CAAC;AAGhC,KAAK,UAAU,SAAS,CAAC,KAAa,EAAE,IAAY,EAAE,EAAuB;IAClF,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE;QAC9B,EAAE,CAAC,CAAC,CAAC,CAAC;QACN,+FAA+F;QAC/F,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;QAC7B,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI,EAAE,SAAS;QACvC,MAAM,QAAQ,EAAE,CAAC;QACjB,EAAE,IAAI,IAAI,CAAC;KACZ;AACH,CAAC;AASK,SAAU,WAAW,CAAC,GAAW;IACrC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAI,KAAK,CAAC,CAAA,iCAAA,EAAoC,OAAO,GAAG,EAAE,CAAC,CAAC;IAC/F,OAAO,IAAI,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,4BAA4B;AACpF,CAAC;AAQK,SAAU,OAAO,CAAC,IAAW;IACjC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,CAAA,yBAAA,EAA4B,OAAO,IAAI,EAAE,CAAC,CAAC;IAC3E,OAAO,IAAI,CAAC;AACd,CAAC;AAKK,SAAU,WAAW,CAAC,GAAG,MAAoB;IACjD,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAG,CAAD,EAAI,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IACvE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,uDAAuD;IACpE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACd,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC;IAClB,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,CAAC;AACX,CAAC;AAGK,MAAgB,IAAI;IAqBxB,0CAA0C;IAC1C,KAAK,GAAA;QACH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;CACF;AAaD,MAAM,KAAK,GAAG,CAAA,CAAE,CAAC,QAAQ,CAAC;AAEpB,SAAU,SAAS,CACvB,QAAY,EACZ,IAAS;IAET,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,iBAAiB,EAC9D,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC7C,OAAO,MAAiB,CAAC;AAC3B,CAAC;AAIK,SAAU,eAAe,CAAoB,QAAuB;IACxE,MAAM,KAAK,GAAG,CAAC,GAAU,EAAc,CAAG,CAAD,OAAS,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACnF,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC;IACvB,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;IAChC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,KAAK,CAAC,MAAM,GAAG,GAAG,CAAG,CAAD,OAAS,EAAE,CAAC;IAChC,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAU,uBAAuB,CACrC,QAA+B;IAE/B,MAAM,KAAK,GAAG,CAAC,GAAU,EAAE,IAAQ,EAAc,CAAG,CAAD,OAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACjG,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAA,CAAO,CAAC,CAAC;IAC9B,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;IAChC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,KAAK,CAAC,MAAM,GAAG,CAAC,IAAO,EAAE,CAAG,CAAD,OAAS,CAAC,IAAI,CAAC,CAAC;IAC3C,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAU,0BAA0B,CACxC,QAAkC;IAElC,MAAM,KAAK,GAAG,CAAC,GAAU,EAAE,IAAQ,EAAc,CAAG,CAAD,OAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACjG,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAA,CAAO,CAAC,CAAC;IAC9B,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;IAChC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,KAAK,CAAC,MAAM,GAAG,CAAC,IAAO,EAAE,CAAG,CAAD,OAAS,CAAC,IAAI,CAAC,CAAC;IAC3C,OAAO,KAAK,CAAC;AACf,CAAC;AAKK,SAAU,WAAW,CAAC,WAAW,GAAG,EAAE;IAC1C,2JAAI,SAAM,IAAI,8JAAO,SAAM,CAAC,eAAe,KAAK,UAAU,EAAE;QAC1D,8JAAO,SAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;KAC5D;IACD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/sha3.js", "sourceRoot": "", "sources": ["../src/sha3.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAClE,OAAO,EACL,IAAI,EACJ,GAAG,EAEH,OAAO,EACP,eAAe,EACf,0BAA0B,GAE3B,MAAM,YAAY,CAAC;;;;AAEpB,oGAAoG;AACpG,iCAAiC;AAEjC,2CAA2C;AAC3C,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,GAAmC;IAAC,EAAE;IAAE,EAAE;IAAE,EAAE;CAAC,CAAC;AACtF,MAAM,GAAG,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,GAAG,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,GAAG,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,GAAG,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,KAAK,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1C,MAAM,MAAM,GAAG,aAAA,EAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,EAAE,CAAE;IAC9D,KAAK;IACL,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;QAAC,CAAC;QAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;KAAC,CAAC;IAClC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,aAAa;IACb,SAAS,CAAC,IAAI,CAAC,AAAE,CAAD,AAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC,EAAG,EAAE,CAAC,CAAC;IACvD,OAAO;IACP,IAAI,CAAC,GAAG,GAAG,CAAC;IACZ,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;QAC1B,CAAC,GAAG,CAAC,AAAC,CAAC,IAAI,GAAG,CAAC,EAAI,CAAD,AAAE,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,AAAC,CAAC,GAAG,KAAK,CAAC;QACjD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,GAAG,IAAK,AAAD,CAAE,GAAG,IAAI,aAAA,EAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;KACrE;IACD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACpB;AACD,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,aAAA,EAAe,sJAAC,QAAA,AAAK,EAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAE3E,oCAAoC;AACpC,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAI,CAAF,AAAG,CAAF,EAAK,EAAE,CAAC,CAAC,sJAAC,SAAM,AAAN,EAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,sJAAC,SAAA,AAAM,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChG,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAI,CAAF,AAAG,CAAF,EAAK,EAAE,CAAC,CAAC,sJAAC,SAAM,AAAN,EAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAC,0JAAA,AAAM,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAG1F,SAAU,OAAO,CAAC,CAAc,EAAE,SAAiB,EAAE;IACzD,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjC,8FAA8F;IAC9F,IAAK,IAAI,KAAK,GAAG,EAAE,GAAG,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,EAAE,CAAE;QACjD,UAAU;QACV,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACzF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAE;YAC9B,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC1B,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC1B,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACnB,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC1C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAE;gBAC/B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;gBACf,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;aACpB;SACF;QACD,qBAAqB;QACrB,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YAC3B,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACpC,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACpC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;YACb,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YACX,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;SAChB;QACD,UAAU;QACV,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAE;YAC/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;SAC7E;QACD,WAAW;QACX,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;KAC5B;IACD,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC;AAEK,MAAO,MAAO,2JAAQ,OAAY;IAOtC,2DAA2D;IAC3D,YACS,QAAgB,EAChB,MAAc,EACd,SAAiB,EACd,YAAY,KAAK,EACjB,SAAiB,EAAE,CAAA;QAE7B,KAAK,EAAE,CAAC;QAND,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAQ;QAChB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAQ;QACd,IAAA,CAAA,SAAS,GAAT,SAAS,CAAQ;QACd,IAAA,CAAA,SAAS,GAAT,SAAS,CAAQ;QACjB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAa;QAXrB,IAAA,CAAA,GAAG,GAAG,CAAC,CAAC;QACR,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QACX,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QAEjB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAU1B,mCAAmC;+JACnC,UAAA,AAAM,EAAC,SAAS,CAAC,CAAC;QAClB,uDAAuD;QACvD,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,EAC5C,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,4JAAA,AAAG,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IACS,MAAM,GAAA;QACd,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IACD,MAAM,CAAC,IAAW,EAAA;gKAChB,SAAA,AAAM,EAAC,IAAI,CAAC,CAAC;QACb,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACjC,IAAI,yJAAG,UAAA,AAAO,EAAC,IAAI,CAAC,CAAC;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAI;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACtD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YAChE,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACS,MAAM,GAAA;QACd,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC9C,iBAAiB;QACjB,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QACjE,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;QAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IACS,SAAS,CAAC,GAAe,EAAA;gKACjC,SAAA,AAAM,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACpB,gKAAA,AAAK,EAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;QAC7B,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAI;YAC/C,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACzD,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YAClE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;YACpB,GAAG,IAAI,IAAI,CAAC;SACb;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IACD,OAAO,CAAC,GAAe,EAAA;QACrB,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IACD,GAAG,CAAC,KAAa,EAAA;gKACf,SAAA,AAAM,EAAC,KAAK,CAAC,CAAC;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD,UAAU,CAAC,GAAe,EAAA;gKACxB,SAAA,AAAM,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAClB,IAAI,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,OAAO,GAAG,CAAC;IACb,CAAC;IACD,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACzD,CAAC;IACD,OAAO,GAAA;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACD,UAAU,CAAC,EAAW,EAAA;QACpB,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAChE,EAAE,IAAA,CAAF,EAAE,GAAK,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,EAAC;QAClE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAClB,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;QACnB,8BAA8B;QAC9B,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;QACnB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;QACzB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;QACzB,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9B,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AAED,MAAM,GAAG,GAAG,CAAC,MAAc,EAAE,QAAgB,EAAE,SAAiB,EAAE,CAChE,CADkE,uKACnD,AAAf,EAAgB,GAAG,CAAG,CAAD,GAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;AAE1D,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAKzD,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACzD,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACzD,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACxD,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAK3D,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3D,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3D,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAIjE,MAAM,QAAQ,GAAG,CAAC,MAAc,EAAE,QAAgB,EAAE,SAAiB,EAAE,EAAE,qJACvE,6BAA0B,AAA1B,EACE,CAAC,OAAkB,CAAA,CAAE,EAAE,CACrB,CADuB,GACnB,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CACxF,CAAC;AAEG,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9D,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/_sha2.js", "sourceRoot": "", "sources": ["../src/_sha2.ts"], "sourcesContent": [], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,IAAI,EAAE,UAAU,EAAS,OAAO,EAAE,MAAM,YAAY,CAAC;;;AAE9D,yBAAyB;AACzB,SAAS,YAAY,CAAC,IAAc,EAAE,UAAkB,EAAE,KAAa,EAAE,IAAa;IACpF,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/F,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IACxB,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;IACpC,MAAM,EAAE,GAAG,MAAM,CAAC,AAAC,KAAK,IAAI,IAAI,CAAC,EAAG,QAAQ,CAAC,CAAC;IAC9C,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;IACpC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IACzC,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC3C,CAAC;AAGK,MAAgB,IAAwB,2JAAQ,OAAO;IAc3D,YACW,QAAgB,EAClB,SAAiB,EACf,SAAiB,EACjB,IAAa,CAAA;QAEtB,KAAK,EAAE,CAAC;QALC,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAQ;QAClB,IAAA,CAAA,SAAS,GAAT,SAAS,CAAQ;QACf,IAAA,CAAA,SAAS,GAAT,SAAS,CAAQ;QACjB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAS;QATd,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QACX,IAAA,CAAA,GAAG,GAAG,CAAC,CAAC;QACR,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAS1B,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,yJAAG,aAAA,AAAU,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IACD,MAAM,CAAC,IAAW,EAAA;gKAChB,SAAA,AAAM,EAAC,IAAI,CAAC,CAAC;QACb,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QACxC,IAAI,yJAAG,UAAA,AAAO,EAAC,IAAI,CAAC,CAAC;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAI;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACtD,8EAA8E;YAC9E,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,MAAM,QAAQ,yJAAG,aAAA,AAAU,EAAC,IAAI,CAAC,CAAC;gBAClC,MAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,QAAQ,CAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC3E,SAAS;aACV;YACD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YACrD,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;YACjB,GAAG,IAAI,IAAI,CAAC;YACZ,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE;gBACzB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACd;SACF;QACD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,UAAU,CAAC,GAAe,EAAA;SACxB,gKAAA,AAAM,EAAC,IAAI,CAAC,CAAC;gKACb,SAAA,AAAM,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,UAAU;QACV,iEAAiE;QACjE,sEAAsE;QACtE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QAC9C,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACnB,oCAAoC;QACpC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,sHAAsH;QACtH,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,GAAG,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACtB,GAAG,GAAG,CAAC,CAAC;SACT;QACD,uCAAuC;QACvC,IAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnD,gGAAgG;QAChG,oFAAoF;QACpF,iDAAiD;QACjD,YAAY,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACtB,MAAM,KAAK,yJAAG,aAAA,AAAU,EAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3B,yFAAyF;QACzF,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QAC5E,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACjF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAC1E,CAAC;IACD,MAAM,GAAA;QACJ,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxB,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,OAAO,GAAG,CAAC;IACb,CAAC;IACD,UAAU,CAAC,EAAM,EAAA;QACf,EAAE,IAAA,CAAF,EAAE,GAAK,IAAK,IAAI,CAAC,WAAmB,EAAO,EAAC;QAC5C,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACtB,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACpE,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;QACnB,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;QACb,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACvB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;QACzB,IAAI,MAAM,GAAG,QAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,EAAE,CAAC;IACZ,CAAC;CACF", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/ripemd160.js", "sourceRoot": "", "sources": ["../src/ripemd160.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;;;AAE7C,0DAA0D;AAC1D,6EAA6E;AAC7E,MAAM,GAAG,GAAG,aAAA,EAAe,CAAC,IAAI,UAAU,CAAC;IAAC,CAAC;IAAE,CAAC;IAAE,EAAE;IAAE,CAAC;IAAE,EAAE;IAAE,CAAC;IAAE,EAAE;IAAE,CAAC;IAAE,EAAE;IAAE,CAAC;IAAE,CAAC;IAAE,CAAC;IAAE,CAAC;IAAE,EAAE;IAAE,EAAE;IAAE,CAAC;CAAC,CAAC,CAAC;AACnG,MAAM,EAAE,GAAG,aAAA,EAAe,CAAC,UAAU,CAAC,IAAI,CAAC;IAAE,MAAM,EAAE,EAAE;AAAA,CAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,CAAC;AACxE,MAAM,EAAE,GAAG,aAAA,EAAe,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3D,IAAI,IAAI,GAAG;IAAC,EAAE;CAAC,CAAC;AAChB,IAAI,IAAI,GAAG;IAAC,EAAE;CAAC,CAAC;AAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,KAAK,IAAI,CAAC,IAAI;IAAC,IAAI;IAAE,IAAI;CAAC,CAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,EAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAExF,MAAM,MAAM,GAAG,aAAA,EAAe,CAAC;IAC7B;QAAC,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;KAAC;IACxD;QAAC,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;KAAC;IACxD;QAAC,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;KAAC;IACxD;QAAC,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;KAAC;IACxD;QAAC,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;KAAC;CACzD,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,GAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,OAAO,GAAG,aAAA,EAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAG,CAAD,EAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,KAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,MAAM,OAAO,GAAG,aAAA,EAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAG,CAAD,EAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,KAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,MAAM,EAAE,GAAG,aAAA,EAAe,CAAC,IAAI,WAAW,CAAC;IACzC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAC3D,CAAC,CAAC;AACH,MAAM,EAAE,GAAG,aAAA,EAAe,CAAC,IAAI,WAAW,CAAC;IACzC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAC3D,CAAC,CAAC;AACH,6DAA6D;AAC7D,MAAM,IAAI,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,CAAG,AAAC,CAAF,GAAM,IAAI,KAAK,CAAC,EAAI,CAAD,GAAK,KAAK,AAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AACxF,2BAA2B;AAC3B,SAAS,CAAC,CAAC,KAAa,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;IACvD,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC7B,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO,AAAC,CAAC,GAAG,CAAC,CAAC,EAAI,CAAD,AAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SAC3C,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACrC,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO,AAAC,CAAC,GAAG,CAAC,CAAC,EAAI,CAAD,AAAE,GAAG,CAAC,CAAC,CAAC,CAAC;SAC3C,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC;AACD,4DAA4D;AAC5D,MAAM,GAAG,GAAG,aAAA,EAAe,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AAC1C,MAAO,SAAU,2JAAQ,OAAe;IAO5C,aAAA;QACE,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAPjB,IAAA,CAAA,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;IAI5B,CAAC;IACS,GAAG,GAAA;QACX,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;QACpC,OAAO;YAAC,EAAE;YAAE,EAAE;YAAE,EAAE;YAAE,EAAE;YAAE,EAAE;SAAC,CAAC;IAC9B,CAAC;IACS,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAA;QACtE,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACnB,CAAC;IACS,OAAO,CAAC,IAAc,EAAE,MAAc,EAAA;QAC9C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAChF,kBAAkB;QAClB,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EACzB,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EACzB,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EACzB,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EACzB,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC;QAE9B,0DAA0D;QAC1D,gEAAgE;QAChE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,CAAE;YACtC,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC;YACzB,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAC1D,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAC5D,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB;YAClE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBAC3B,MAAM,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAG,CAAC,CAAC;gBAChF,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,kBAAkB;aAC9E;YACD,yBAAyB;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBAC3B,MAAM,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAG,CAAC,CAAC;gBACjF,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,kBAAkB;aAC9E;SACF;QACD,qDAAqD;QACrD,IAAI,CAAC,GAAG,CACN,AAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAG,CAAC,EACvB,AAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAG,CAAC,EACtB,AAAD,IAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAG,CAAC,EACvB,AAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAG,CAAC,EACvB,AAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAG,CAAC,CACxB,CAAC;IACJ,CAAC;IACS,UAAU,GAAA;QAClB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;IACD,OAAO,GAAA;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B,CAAC;CACF;AAMM,MAAM,SAAS,GAAG,aAAA,EAAe,uJAAC,kBAAA,AAAe,EAAC,GAAG,CAAG,CAAD,GAAK,SAAS,EAAE,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/sha256.js", "sourceRoot": "", "sources": ["../src/sha256.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;;;AAEnD,gEAAgE;AAChE,0DAA0D;AAE1D,oBAAoB;AACpB,MAAM,GAAG,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAF,AAAG,GAAG,CAAC,CAAC,EAAI,CAAD,AAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACpE,oDAAoD;AACpD,MAAM,GAAG,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAG,AAAC,CAAF,AAAG,GAAG,CAAC,CAAC,EAAI,CAAD,AAAE,GAAG,CAAC,CAAC,EAAI,CAAD,AAAE,GAAG,CAAC,CAAC,CAAC;AAE7E,mBAAmB;AACnB,yFAAyF;AACzF,kBAAkB;AAClB,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAA,IAAI,WAAW,CAAC;IAC9C,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAC9F,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAC9F,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAC9F,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAC9F,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAC9F,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAC9F,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAC9F,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAC/F,CAAC,CAAC;AAEH,yGAAyG;AACzG,kBAAkB;AAClB,MAAM,EAAE,GAAG,aAAA,EAAe,CAAA,IAAI,WAAW,CAAC;IACxC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAC/F,CAAC,CAAC;AAEH,4DAA4D;AAC5D,mDAAmD;AACnD,MAAM,QAAQ,GAAG,aAAA,EAAe,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AACrD,MAAM,MAAO,2JAAQ,OAAY;IAY/B,aAAA;QACE,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QAZ1B,mEAAmE;QACnE,uDAAuD;QACvD,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAId,CAAC;IACS,GAAG,GAAA;QACX,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QACxC,OAAO;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;IAClC,CAAC;IACD,kBAAkB;IACR,GAAG,CACX,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;QAEtF,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IACS,OAAO,CAAC,IAAc,EAAE,MAAc,EAAA;QAC9C,gGAAgG;QAChG,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACtF,IAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YAC5B,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC7B,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3B,MAAM,EAAE,yJAAG,OAAI,AAAJ,EAAK,GAAG,EAAE,CAAC,CAAC,yJAAG,OAAA,AAAI,EAAC,GAAG,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,KAAK,CAAC,CAAC,CAAC;YACtD,MAAM,EAAE,GAAG,6JAAA,AAAI,EAAC,EAAE,EAAE,EAAE,CAAC,yJAAG,OAAA,AAAI,EAAC,EAAE,EAAE,EAAE,CAAC,GAAG,AAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YACrD,QAAQ,CAAC,CAAC,CAAC,GAAG,AAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,CAAC,CAAC;SAClE;QACD,4CAA4C;QAC5C,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QACtC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YAC3B,MAAM,MAAM,yJAAG,OAAA,AAAI,EAAC,CAAC,EAAE,CAAC,CAAC,yJAAG,OAAA,AAAI,EAAC,CAAC,EAAE,EAAE,CAAC,GAAG,6JAAI,AAAJ,EAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,MAAM,EAAE,GAAG,AAAC,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;YACvE,MAAM,MAAM,yJAAG,OAAA,AAAI,EAAC,CAAC,EAAE,CAAC,CAAC,yJAAG,OAAA,AAAI,EAAC,CAAC,EAAE,EAAE,CAAC,IAAG,4JAAA,AAAI,EAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,MAAM,EAAE,GAAG,AAAC,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;YACvC,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,AAAC,CAAC,GAAG,EAAE,CAAC,EAAG,CAAC,CAAC;YACjB,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAI,AAAD,EAAG,GAAG,EAAE,CAAC,EAAG,CAAC,CAAC;SACnB;QACD,qDAAqD;QACrD,CAAC,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,CAAC,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,CAAC,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,CAAC,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,CAAC,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,CAAC,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,CAAC,GAAI,AAAD,CAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,CAAC,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAG,CAAC,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IACS,UAAU,GAAA;QAClB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IACD,OAAO,GAAA;QACL,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;CACF;AACD,4EAA4E;AAC5E,MAAM,MAAO,SAAQ,MAAM;IASzB,aAAA;QACE,KAAK,EAAE,CAAC;QATV,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;QAGjB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACtB,CAAC;CACF;AAMM,MAAM,MAAM,GAAG,aAAA,EAAe,uJAAC,kBAAA,AAAe,EAAC,GAAG,CAAG,CAAD,GAAK,MAAM,EAAE,CAAC,CAAC;AACnE,MAAM,MAAM,GAAG,aAAA,EAAe,uJAAC,kBAAA,AAAe,EAAC,GAAG,CAAG,CAAD,GAAK,MAAM,EAAE,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/hmac.js", "sourceRoot": "", "sources": ["../src/hmac.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,IAAI,IAAI,UAAU,EAAE,KAAK,IAAI,WAAW,EAAE,MAAM,IAAI,YAAY,EAAE,MAAM,cAAc,CAAC;AAChG,OAAO,EAAE,IAAI,EAAgB,OAAO,EAAE,MAAM,YAAY,CAAC;;;AAEnD,MAAO,IAAwB,SAAQ,yJAAa;IAQxD,YAAY,IAAW,EAAE,IAAW,CAAA;QAClC,KAAK,EAAE,CAAC;QAJF,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;gKAIxB,OAAU,AAAV,EAAW,IAAI,CAAC,CAAC;QACjB,MAAM,GAAG,GAAG,gKAAA,AAAO,EAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAO,CAAC;QAChC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,UAAU,EACzC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;QACrC,wCAAwC;QACxC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QACpD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,mHAAmH;QACnH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAO,CAAC;QAChC,uCAAuC;QACvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;IACD,MAAM,CAAC,GAAU,EAAA;QACf,iKAAA,AAAY,EAAC,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,UAAU,CAAC,GAAe,EAAA;gKACxB,SAAA,AAAY,EAAC,IAAI,CAAC,CAAC;gKACnB,QAAA,AAAW,EAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IACD,MAAM,GAAA;QACJ,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrB,OAAO,GAAG,CAAC;IACb,CAAC;IACD,UAAU,CAAC,EAAY,EAAA;QACrB,mGAAmG;QACnG,EAAE,IAAA,CAAF,EAAE,GAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAA,CAAE,CAAC,EAAC;QACtD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QACxE,EAAE,GAAG,EAAU,CAAC;QAChB,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACvB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;QACzB,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACvB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;QACzB,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACtC,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACtC,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,OAAO,GAAA;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;CACF;AAQM,MAAM,IAAI,GAAG,CAAC,IAAW,EAAE,GAAU,EAAE,OAAc,EAAc,CACxE,CAD0E,GACtE,IAAI,CAAM,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;AACpD,IAAI,CAAC,MAAM,GAAG,CAAC,IAAW,EAAE,GAAU,EAAE,CAAG,CAAD,GAAK,IAAI,CAAM,IAAI,EAAE,GAAG,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/pbkdf2.js", "sourceRoot": "", "sources": ["../src/pbkdf2.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,IAAI,IAAI,UAAU,EAAE,MAAM,IAAI,YAAY,EAAE,MAAM,cAAc,CAAC;AAC1E,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EAAsB,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;;;;AAQ3F,wDAAwD;AACxD,SAAS,UAAU,CAAC,IAAW,EAAE,SAAgB,EAAE,KAAY,EAAE,KAAgB;4JAC/E,OAAA,AAAU,EAAC,IAAI,CAAC,CAAC;IACjB,MAAM,IAAI,wJAAG,aAAA,AAAS,EAAC;QAAE,KAAK,EAAE,EAAE;QAAE,SAAS,EAAE,EAAE;IAAA,CAAE,EAAE,KAAK,CAAC,CAAC;IAC5D,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;KACrC,gKAAA,AAAY,EAAC,CAAC,CAAC,CAAC;4JAChB,SAAA,AAAY,EAAC,KAAK,CAAC,CAAC;2JACpB,UAAA,AAAY,EAAC,SAAS,CAAC,CAAC;IACxB,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IACpE,MAAM,QAAQ,OAAG,4JAAA,AAAO,EAAC,SAAS,CAAC,CAAC;IACpC,MAAM,IAAI,yJAAG,UAAA,AAAO,EAAC,KAAK,CAAC,CAAC;IAC5B,8CAA8C;IAC9C,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IACjC,0CAA0C;IAC1C,MAAM,GAAG,oJAAG,OAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACxC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9C,OAAO;QAAE,CAAC;QAAE,KAAK;QAAE,SAAS;QAAE,EAAE;QAAE,GAAG;QAAE,OAAO;IAAA,CAAE,CAAC;AACnD,CAAC;AAED,SAAS,YAAY,CACnB,GAAY,EACZ,OAAgB,EAChB,EAAc,EACd,IAAa,EACb,CAAa;IAEb,GAAG,CAAC,OAAO,EAAE,CAAC;IACd,OAAO,CAAC,OAAO,EAAE,CAAC;IAClB,IAAI,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACV,OAAO,EAAE,CAAC;AACZ,CAAC;AASK,SAAU,MAAM,CAAC,IAAW,EAAE,QAAe,EAAE,IAAW,EAAE,IAAe;IAC/E,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9E,IAAI,IAAS,CAAC,CAAC,eAAe;IAC9B,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM,IAAI,yJAAG,aAAA,AAAU,EAAC,GAAG,CAAC,CAAC;IAC7B,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxC,iCAAiC;IACjC,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,CAAC,SAAS,CAAE;QACjE,+BAA+B;QAC/B,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5B,6CAA6C;QAC7C,0CAA0C;QAC1C,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5D,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;QACjC,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAE;YAC7B,2BAA2B;YAC3B,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACnD;KACF;IACD,OAAO,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACjD,CAAC;AAEM,KAAK,UAAU,WAAW,CAAC,IAAW,EAAE,QAAe,EAAE,IAAW,EAAE,IAAe;IAC1F,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACzF,IAAI,IAAS,CAAC,CAAC,eAAe;IAC9B,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM,IAAI,yJAAG,aAAA,AAAU,EAAC,GAAG,CAAC,CAAC;IAC7B,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxC,iCAAiC;IACjC,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,CAAC,SAAS,CAAE;QACjE,+BAA+B;QAC/B,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5B,6CAA6C;QAC7C,0CAA0C;QAC1C,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5D,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;QACjC,OAAM,iKAAA,AAAS,EAAC,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE;YACrC,2BAA2B;YAC3B,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;KACJ;IACD,OAAO,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACjD,CAAC", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@noble/hashes/esm/scrypt.js", "sourceRoot": "", "sources": ["../src/scrypt.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,MAAM,IAAI,YAAY,EAAE,MAAM,cAAc,CAAC;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAS,GAAG,EAAE,MAAM,YAAY,CAAC;;;;;AAE9D,sBAAsB;AAEtB,yBAAyB;AACzB,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAI,AAAD,CAAE,AAAH,IAAO,CAAC,CAAC,EAAI,CAAD,AAAE,KAAK,AAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAEnE,gDAAgD;AAChD,oEAAoE;AACpE,kBAAkB;AAClB,SAAS,WAAW,CAClB,IAAiB,EACjB,EAAU,EACV,KAAkB,EAClB,EAAU,EACV,GAAgB,EAChB,EAAU;IAEV,yCAAyC;IACzC,aAAa;IACb,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,4CAA4C;IAC5C,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAC1C,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAC1C,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAC1C,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;IAC/C,oBAAoB;IACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE;QAC7B,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC;QAC/D,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;KAChE;IACD,uBAAuB;IACvB,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IACzD,GAAG,CAAC,EAAE,EAAE,CAAC,GAAI,AAAD,GAAI,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,QAAQ,CAAC,KAAkB,EAAE,EAAU,EAAE,GAAgB,EAAE,EAAU,EAAE,CAAS;IACvF,8EAA8E;IAC9E,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IAClB,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACvB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc;IAC7F,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAE;QAChD,qEAAqE;QACrE,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,4CAA4C;QAC1F,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC,+CAA+C;QACtE,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,AAAC,EAAE,IAAI,EAAE,CAAC,CAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,4CAA4C;KACnG;AACH,CAAC;AAYD,wDAAwD;AACxD,SAAS,UAAU,CAAC,QAAe,EAAE,IAAW,EAAE,KAAkB;IAClE,8BAA8B;IAC9B,MAAM,IAAI,OAAG,8JAAS,AAAT,EACX;QACE,KAAK,EAAE,EAAE;QACT,SAAS,EAAE,EAAE;QACb,MAAM,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI;KACzB,EACD,KAAK,CACN,CAAC;IACF,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;KAC/D,gKAAA,AAAY,EAAC,CAAC,CAAC,CAAC;4JAChB,SAAA,AAAY,EAAC,CAAC,CAAC,CAAC;QAChB,6JAAA,AAAY,EAAC,CAAC,CAAC,CAAC;4JAChB,SAAA,AAAY,EAAC,KAAK,CAAC,CAAC;4JACpB,SAAA,AAAY,EAAC,SAAS,CAAC,CAAC;4JACxB,SAAA,AAAY,EAAC,MAAM,CAAC,CAAC;IACrB,IAAI,UAAU,KAAK,SAAS,IAAI,OAAO,UAAU,KAAK,UAAU,EAC9D,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACnD,MAAM,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC;IAC1B,MAAM,WAAW,GAAG,SAAS,GAAG,CAAC,CAAC;IAClC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,AAAC,CAAC,GAAG,CAAE,AAAD,CAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;QAC7E,yFAAyF;QACzF,oHAAoH;QACpH,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAC;KACH;IACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,AAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAG,SAAS,EAAE;QACjD,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;KACH;IACD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAC3C,MAAM,IAAI,KAAK,CACb,gFAAgF,CACjF,CAAC;KACH;IACD,MAAM,OAAO,GAAG,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpC,IAAI,OAAO,GAAG,MAAM,EAAE;QACpB,MAAM,IAAI,KAAK,CACb,CAAA,8BAAA,EAAiC,OAAO,CAAA,uBAAA,EAA0B,MAAM,CAAA,SAAA,CAAW,CACpF,CAAC;KACH;IACD,wFAAwF;IACxF,0EAA0E;IAC1E,MAAM,CAAC,0JAAG,SAAA,AAAM,qJAAC,SAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;QAAE,CAAC,EAAE,CAAC;QAAE,KAAK,EAAE,SAAS,GAAG,CAAC;IAAA,CAAE,CAAC,CAAC;IACzE,MAAM,GAAG,yJAAG,MAAG,AAAH,EAAI,CAAC,CAAC,CAAC;IACnB,8DAA8D;IAC9D,MAAM,CAAC,yJAAG,MAAA,AAAG,EAAC,IAAI,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAM,GAAG,yJAAG,MAAA,AAAG,EAAC,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3C,IAAI,UAAU,GAAG,GAAG,EAAE,AAAE,CAAC,CAAC;IAC1B,IAAI,UAAU,EAAE;QACd,MAAM,aAAa,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChC,0DAA0D;QAC1D,wDAAwD;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,UAAU,GAAG,GAAG,EAAE;YAChB,WAAW,EAAE,CAAC;YACd,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,WAAW,KAAK,aAAa,CAAC,EAC/E,UAAU,CAAC,WAAW,GAAG,aAAa,CAAC,CAAC;QAC5C,CAAC,CAAC;KACH;IACD,OAAO;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,KAAK;QAAE,WAAW;QAAE,CAAC;QAAE,GAAG;QAAE,CAAC;QAAE,GAAG;QAAE,UAAU;QAAE,SAAS;IAAA,CAAE,CAAC;AAChF,CAAC;AAED,SAAS,YAAY,CACnB,QAAe,EACf,KAAa,EACb,CAAa,EACb,CAAc,EACd,GAAgB;IAEhB,MAAM,GAAG,0JAAG,SAAA,AAAM,qJAAC,SAAM,EAAE,QAAQ,EAAE,CAAC,EAAE;QAAE,CAAC,EAAE,CAAC;QAAE,KAAK;IAAA,CAAE,CAAC,CAAC;IACzD,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACV,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACZ,OAAO,GAAG,CAAC;AACb,CAAC;AAgBK,SAAU,MAAM,CAAC,QAAe,EAAE,IAAW,EAAE,IAAgB;IACnE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,UAAU,CAC5E,QAAQ,EACR,IAAI,EACJ,IAAI,CACL,CAAC;IACF,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAE;QAC7B,MAAM,EAAE,GAAG,WAAW,GAAG,EAAE,CAAC;QAC5B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc;QACxE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YACvC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAC,GAAG,IAAI,WAAW,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,2BAA2B;YACzE,UAAU,EAAE,CAAC;SACd;QACD,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,uBAAuB;QACvE,UAAU,EAAE,CAAC;QACb,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAC1B,kDAAkD;YAClD,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,WAAW,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,gCAAgC;YAC1E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB;YACtG,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;YACvD,UAAU,EAAE,CAAC;SACd;KACF;IACD,OAAO,YAAY,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAClD,CAAC;AAKM,KAAK,UAAU,WAAW,CAAC,QAAe,EAAE,IAAW,EAAE,IAAgB;IAC9E,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,UAAU,CACvF,QAAQ,EACR,IAAI,EACJ,IAAI,CACL,CAAC;IACF,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAE;QAC7B,MAAM,EAAE,GAAG,WAAW,GAAG,EAAE,CAAC;QAC5B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc;QACxE,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,4JAAM,YAAA,AAAS,EAAC,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE;YACrC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,AAAC,GAAG,IAAI,WAAW,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,2BAA2B;YACzE,UAAU,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;QACH,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,uBAAuB;QACvE,UAAU,EAAE,CAAC;QACb,4JAAM,YAAA,AAAS,EAAC,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE;YACjC,kDAAkD;YAClD,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,WAAW,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,gCAAgC;YAC1E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB;YACtG,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;YACvD,UAAU,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;KACJ;IACD,OAAO,YAAY,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAClD,CAAC", "debugId": null}}, {"offset": {"line": 1513, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/%40adraffy/ens-normalize/dist/index.mjs"], "sourcesContent": ["// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-ens data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: 0565ed049b9cf1614bb9e11ba7d8ac6a6fb96c893253d890f7e2b2884b9ded32\nvar COMPRESSED$1 = '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';\nconst FENCED = new Map([[8217,\"apostrophe\"],[8260,\"fraction slash\"],[12539,\"middle dot\"]]);\nconst NSM_MAX = 4;\n\nfunction decode_arithmetic(bytes) {\r\n\tlet pos = 0;\r\n\tfunction u16() { return (bytes[pos++] << 8) | bytes[pos++]; }\r\n\t\r\n\t// decode the frequency table\r\n\tlet symbol_count = u16();\r\n\tlet total = 1;\r\n\tlet acc = [0, 1]; // first symbol has frequency 1\r\n\tfor (let i = 1; i < symbol_count; i++) {\r\n\t\tacc.push(total += u16());\r\n\t}\r\n\r\n\t// skip the sized-payload that the last 3 symbols index into\r\n\tlet skip = u16();\r\n\tlet pos_payload = pos;\r\n\tpos += skip;\r\n\r\n\tlet read_width = 0;\r\n\tlet read_buffer = 0; \r\n\tfunction read_bit() {\r\n\t\tif (read_width == 0) {\r\n\t\t\t// this will read beyond end of buffer\r\n\t\t\t// but (undefined|0) => zero pad\r\n\t\t\tread_buffer = (read_buffer << 8) | bytes[pos++];\r\n\t\t\tread_width = 8;\r\n\t\t}\r\n\t\treturn (read_buffer >> --read_width) & 1;\r\n\t}\r\n\r\n\tconst N = 31;\r\n\tconst FULL = 2**N;\r\n\tconst HALF = FULL >>> 1;\r\n\tconst QRTR = HALF >> 1;\r\n\tconst MASK = FULL - 1;\r\n\r\n\t// fill register\r\n\tlet register = 0;\r\n\tfor (let i = 0; i < N; i++) register = (register << 1) | read_bit();\r\n\r\n\tlet symbols = [];\r\n\tlet low = 0;\r\n\tlet range = FULL; // treat like a float\r\n\twhile (true) {\r\n\t\tlet value = Math.floor((((register - low + 1) * total) - 1) / range);\r\n\t\tlet start = 0;\r\n\t\tlet end = symbol_count;\r\n\t\twhile (end - start > 1) { // binary search\r\n\t\t\tlet mid = (start + end) >>> 1;\r\n\t\t\tif (value < acc[mid]) {\r\n\t\t\t\tend = mid;\r\n\t\t\t} else {\r\n\t\t\t\tstart = mid;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (start == 0) break; // first symbol is end mark\r\n\t\tsymbols.push(start);\r\n\t\tlet a = low + Math.floor(range * acc[start]   / total);\r\n\t\tlet b = low + Math.floor(range * acc[start+1] / total) - 1;\r\n\t\twhile (((a ^ b) & HALF) == 0) {\r\n\t\t\tregister = (register << 1) & MASK | read_bit();\r\n\t\t\ta = (a << 1) & MASK;\r\n\t\t\tb = (b << 1) & MASK | 1;\r\n\t\t}\r\n\t\twhile (a & ~b & QRTR) {\r\n\t\t\tregister = (register & HALF) | ((register << 1) & (MASK >>> 1)) | read_bit();\r\n\t\t\ta = (a << 1) ^ HALF;\r\n\t\t\tb = ((b ^ HALF) << 1) | HALF | 1;\r\n\t\t}\r\n\t\tlow = a;\r\n\t\trange = 1 + b - a;\r\n\t}\r\n\tlet offset = symbol_count - 4;\r\n\treturn symbols.map(x => { // index into payload\r\n\t\tswitch (x - offset) {\r\n\t\t\tcase 3: return offset + 0x10100 + ((bytes[pos_payload++] << 16) | (bytes[pos_payload++] << 8) | bytes[pos_payload++]);\r\n\t\t\tcase 2: return offset + 0x100 + ((bytes[pos_payload++] << 8) | bytes[pos_payload++]);\r\n\t\t\tcase 1: return offset + bytes[pos_payload++];\r\n\t\t\tdefault: return x - 1;\r\n\t\t}\r\n\t});\r\n}\t\r\n\r\n// returns an iterator which returns the next symbol\r\nfunction read_payload(v) {\r\n\tlet pos = 0;\r\n\treturn () => v[pos++];\r\n}\r\nfunction read_compressed_payload(s) {\r\n\treturn read_payload(decode_arithmetic(unsafe_atob(s)));\r\n}\r\n\r\n// unsafe in the sense:\r\n// expected well-formed Base64 w/o padding \r\n// 20220922: added for https://github.com/adraffy/ens-normalize.js/issues/4\r\nfunction unsafe_atob(s) {\r\n\tlet lookup = [];\r\n\t[...'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'].forEach((c, i) => lookup[c.charCodeAt(0)] = i);\r\n\tlet n = s.length;\r\n\tlet ret = new Uint8Array((6 * n) >> 3);\r\n\tfor (let i = 0, pos = 0, width = 0, carry = 0; i < n; i++) {\r\n\t\tcarry = (carry << 6) | lookup[s.charCodeAt(i)];\r\n\t\twidth += 6;\r\n\t\tif (width >= 8) {\r\n\t\t\tret[pos++] = (carry >> (width -= 8));\r\n\t\t}\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\n// eg. [0,1,2,3...] => [0,-1,1,-2,...]\r\nfunction signed(i) { \r\n\treturn (i & 1) ? (~i >> 1) : (i >> 1);\r\n}\r\n\r\nfunction read_deltas(n, next) {\r\n\tlet v = Array(n);\r\n\tfor (let i = 0, x = 0; i < n; i++) v[i] = x += signed(next());\r\n\treturn v;\r\n}\r\n\r\n// [123][5] => [0 3] [1 1] [0 0]\r\nfunction read_sorted(next, prev = 0) {\r\n\tlet ret = [];\r\n\twhile (true) {\r\n\t\tlet x = next();\r\n\t\tlet n = next();\r\n\t\tif (!n) break;\r\n\t\tprev += x;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tret.push(prev + i);\r\n\t\t}\r\n\t\tprev += n + 1;\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction read_sorted_arrays(next) {\r\n\treturn read_array_while(() => { \r\n\t\tlet v = read_sorted(next);\r\n\t\tif (v.length) return v;\r\n\t});\r\n}\r\n\r\n// returns map of x => ys\r\nfunction read_mapped(next) {\r\n\tlet ret = [];\r\n\twhile (true) {\r\n\t\tlet w = next();\r\n\t\tif (w == 0) break;\r\n\t\tret.push(read_linear_table(w, next));\r\n\t}\r\n\twhile (true) {\r\n\t\tlet w = next() - 1;\r\n\t\tif (w < 0) break;\r\n\t\tret.push(read_replacement_table(w, next));\r\n\t}\r\n\treturn ret.flat();\r\n}\r\n\r\n// read until next is falsy\r\n// return array of read values\r\nfunction read_array_while(next) {\r\n\tlet v = [];\r\n\twhile (true) {\r\n\t\tlet x = next(v.length);\r\n\t\tif (!x) break;\r\n\t\tv.push(x);\r\n\t}\r\n\treturn v;\r\n}\r\n\r\n// read w columns of length n\r\n// return as n rows of length w\r\nfunction read_transposed(n, w, next) {\r\n\tlet m = Array(n).fill().map(() => []);\r\n\tfor (let i = 0; i < w; i++) {\r\n\t\tread_deltas(n, next).forEach((x, j) => m[j].push(x));\r\n\t}\r\n\treturn m;\r\n}\r\n \r\n// returns [[x, ys], [x+dx, ys+dy], [x+2*dx, ys+2*dy], ...]\r\n// where dx/dy = steps, n = run size, w = length of y\r\nfunction read_linear_table(w, next) {\r\n\tlet dx = 1 + next();\r\n\tlet dy = next();\r\n\tlet vN = read_array_while(next);\r\n\tlet m = read_transposed(vN.length, 1+w, next);\r\n\treturn m.flatMap((v, i) => {\r\n\t\tlet [x, ...ys] = v;\r\n\t\treturn Array(vN[i]).fill().map((_, j) => {\r\n\t\t\tlet j_dy = j * dy;\r\n\t\t\treturn [x + j * dx, ys.map(y => y + j_dy)];\r\n\t\t});\r\n\t});\r\n}\r\n\r\n// return [[x, ys...], ...]\r\n// where w = length of y\r\nfunction read_replacement_table(w, next) { \r\n\tlet n = 1 + next();\r\n\tlet m = read_transposed(n, 1+w, next);\r\n\treturn m.map(v => [v[0], v.slice(1)]);\r\n}\r\n\r\n\r\nfunction read_trie(next) {\r\n\tlet ret = [];\r\n\tlet sorted = read_sorted(next); \r\n\texpand(decode([]), []);\r\n\treturn ret; // not sorted\r\n\tfunction decode(Q) { // characters that lead into this node\r\n\t\tlet S = next(); // state: valid, save, check\r\n\t\tlet B = read_array_while(() => { // buckets leading to new nodes\r\n\t\t\tlet cps = read_sorted(next).map(i => sorted[i]);\r\n\t\t\tif (cps.length) return decode(cps);\r\n\t\t});\r\n\t\treturn {S, B, Q};\r\n\t}\r\n\tfunction expand({S, B}, cps, saved) {\r\n\t\tif (S & 4 && saved === cps[cps.length-1]) return;\r\n\t\tif (S & 2) saved = cps[cps.length-1];\r\n\t\tif (S & 1) ret.push(cps); \r\n\t\tfor (let br of B) {\r\n\t\t\tfor (let cp of br.Q) {\r\n\t\t\t\texpand(br, [...cps, cp], saved);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\n\nfunction hex_cp(cp) {\r\n\treturn cp.toString(16).toUpperCase().padStart(2, '0');\r\n}\r\n\r\nfunction quote_cp(cp) {\r\n\treturn `{${hex_cp(cp)}}`; // raffy convention: like \"\\u{X}\" w/o the \"\\u\"\r\n}\r\n\r\n/*\r\nexport function explode_cp(s) {\r\n\treturn [...s].map(c => c.codePointAt(0));\r\n}\r\n*/\r\nfunction explode_cp(s) { // this is about 2x faster\r\n\tlet cps = [];\r\n\tfor (let pos = 0, len = s.length; pos < len; ) {\r\n\t\tlet cp = s.codePointAt(pos);\r\n\t\tpos += cp < 0x10000 ? 1 : 2;\r\n\t\tcps.push(cp);\r\n\t}\r\n\treturn cps;\r\n}\r\n\r\nfunction str_from_cps(cps) {\r\n\tconst chunk = 4096;\r\n\tlet len = cps.length;\r\n\tif (len < chunk) return String.fromCodePoint(...cps);\r\n\tlet buf = [];\r\n\tfor (let i = 0; i < len; ) {\r\n\t\tbuf.push(String.fromCodePoint(...cps.slice(i, i += chunk)));\r\n\t}\r\n\treturn buf.join('');\r\n}\r\n\r\nfunction compare_arrays(a, b) {\r\n\tlet n = a.length;\r\n\tlet c = n - b.length;\r\n\tfor (let i = 0; c == 0 && i < n; i++) c = a[i] - b[i];\r\n\treturn c;\r\n}\n\n// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-nf data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: a974b6f8541fc29d919bc85118af0a44015851fab5343f8679cb31be2bdb209e\nvar COMPRESSED = '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';\n\n// https://unicode.org/reports/tr15/\r\n// for reference implementation\r\n// see: /derive/nf.js\r\n\r\n\r\n// algorithmic hangul\r\n// https://www.unicode.org/versions/Unicode15.0.0/ch03.pdf (page 144)\r\nconst S0 = 0xAC00;\r\nconst L0 = 0x1100;\r\nconst V0 = 0x1161;\r\nconst T0 = 0x11A7;\r\nconst L_COUNT = 19;\r\nconst V_COUNT = 21;\r\nconst T_COUNT = 28;\r\nconst N_COUNT = V_COUNT * T_COUNT;\r\nconst S_COUNT = L_COUNT * N_COUNT;\r\nconst S1 = S0 + S_COUNT;\r\nconst L1 = L0 + L_COUNT;\r\nconst V1 = V0 + V_COUNT;\r\nconst T1 = T0 + T_COUNT;\r\n\r\nfunction unpack_cc(packed) {\r\n\treturn (packed >> 24) & 0xFF;\r\n}\r\nfunction unpack_cp(packed) {\r\n\treturn packed & 0xFFFFFF;\r\n}\r\n\r\nlet SHIFTED_RANK, EXCLUSIONS, DECOMP, RECOMP;\r\n\r\nfunction init$1() {\r\n\t//console.time('nf');\r\n\tlet r = read_compressed_payload(COMPRESSED);\r\n\tSHIFTED_RANK = new Map(read_sorted_arrays(r).flatMap((v, i) => v.map(x => [x, (i+1) << 24]))); // pre-shifted\r\n\tEXCLUSIONS = new Set(read_sorted(r));\r\n\tDECOMP = new Map();\r\n\tRECOMP = new Map();\r\n\tfor (let [cp, cps] of read_mapped(r)) {\r\n\t\tif (!EXCLUSIONS.has(cp) && cps.length == 2) {\r\n\t\t\tlet [a, b] = cps;\r\n\t\t\tlet bucket = RECOMP.get(a);\r\n\t\t\tif (!bucket) {\r\n\t\t\t\tbucket = new Map();\r\n\t\t\t\tRECOMP.set(a, bucket);\r\n\t\t\t}\r\n\t\t\tbucket.set(b, cp);\r\n\t\t}\r\n\t\tDECOMP.set(cp, cps.reverse()); // stored reversed\r\n\t}\r\n\t//console.timeEnd('nf');\r\n\t// 20230905: 11ms\r\n}\r\n\r\nfunction is_hangul(cp) {\r\n\treturn cp >= S0 && cp < S1;\r\n}\r\n\r\nfunction compose_pair(a, b) {\r\n\tif (a >= L0 && a < L1 && b >= V0 && b < V1) {\r\n\t\treturn S0 + (a - L0) * N_COUNT + (b - V0) * T_COUNT;\r\n\t} else if (is_hangul(a) && b > T0 && b < T1 && (a - S0) % T_COUNT == 0) {\r\n\t\treturn a + (b - T0);\r\n\t} else {\r\n\t\tlet recomp = RECOMP.get(a);\r\n\t\tif (recomp) {\r\n\t\t\trecomp = recomp.get(b);\r\n\t\t\tif (recomp) {\r\n\t\t\t\treturn recomp;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn -1;\r\n\t}\r\n}\r\n\r\nfunction decomposed(cps) {\r\n\tif (!SHIFTED_RANK) init$1();\r\n\tlet ret = [];\r\n\tlet buf = [];\r\n\tlet check_order = false;\r\n\tfunction add(cp) {\r\n\t\tlet cc = SHIFTED_RANK.get(cp);\r\n\t\tif (cc) {\r\n\t\t\tcheck_order = true;\r\n\t\t\tcp |= cc;\r\n\t\t}\r\n\t\tret.push(cp);\r\n\t}\r\n\tfor (let cp of cps) {\r\n\t\twhile (true) {\r\n\t\t\tif (cp < 0x80) {\r\n\t\t\t\tret.push(cp);\r\n\t\t\t} else if (is_hangul(cp)) {\r\n\t\t\t\tlet s_index = cp - S0;\r\n\t\t\t\tlet l_index = s_index / N_COUNT | 0;\r\n\t\t\t\tlet v_index = (s_index % N_COUNT) / T_COUNT | 0;\r\n\t\t\t\tlet t_index = s_index % T_COUNT;\r\n\t\t\t\tadd(L0 + l_index);\r\n\t\t\t\tadd(V0 + v_index);\r\n\t\t\t\tif (t_index > 0) add(T0 + t_index);\r\n\t\t\t} else {\r\n\t\t\t\tlet mapped = DECOMP.get(cp);\r\n\t\t\t\tif (mapped) {\r\n\t\t\t\t\tbuf.push(...mapped);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tadd(cp);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (!buf.length) break;\r\n\t\t\tcp = buf.pop();\r\n\t\t}\r\n\t}\r\n\tif (check_order && ret.length > 1) {\r\n\t\tlet prev_cc = unpack_cc(ret[0]);\r\n\t\tfor (let i = 1; i < ret.length; i++) {\r\n\t\t\tlet cc = unpack_cc(ret[i]);\r\n\t\t\tif (cc == 0 || prev_cc <= cc) {\r\n\t\t\t\tprev_cc = cc;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tlet j = i-1;\r\n\t\t\twhile (true) {\r\n\t\t\t\tlet tmp = ret[j+1];\r\n\t\t\t\tret[j+1] = ret[j];\r\n\t\t\t\tret[j] = tmp;\r\n\t\t\t\tif (!j) break;\r\n\t\t\t\tprev_cc = unpack_cc(ret[--j]);\r\n\t\t\t\tif (prev_cc <= cc) break;\r\n\t\t\t}\r\n\t\t\tprev_cc = unpack_cc(ret[i]);\r\n\t\t}\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction composed_from_decomposed(v) {\r\n\tlet ret = [];\r\n\tlet stack = [];\r\n\tlet prev_cp = -1;\r\n\tlet prev_cc = 0;\r\n\tfor (let packed of v) {\r\n\t\tlet cc = unpack_cc(packed);\r\n\t\tlet cp = unpack_cp(packed);\r\n\t\tif (prev_cp == -1) {\r\n\t\t\tif (cc == 0) {\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tret.push(cp);\r\n\t\t\t}\r\n\t\t} else if (prev_cc > 0 && prev_cc >= cc) {\r\n\t\t\tif (cc == 0) {\r\n\t\t\t\tret.push(prev_cp, ...stack);\r\n\t\t\t\tstack.length = 0;\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tstack.push(cp);\r\n\t\t\t}\r\n\t\t\tprev_cc = cc;\r\n\t\t} else {\r\n\t\t\tlet composed = compose_pair(prev_cp, cp);\r\n\t\t\tif (composed >= 0) {\r\n\t\t\t\tprev_cp = composed;\r\n\t\t\t} else if (prev_cc == 0 && cc == 0) {\r\n\t\t\t\tret.push(prev_cp);\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tstack.push(cp);\r\n\t\t\t\tprev_cc = cc;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (prev_cp >= 0) {\r\n\t\tret.push(prev_cp, ...stack);\t\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\n// note: cps can be iterable\r\nfunction nfd(cps) {\r\n\treturn decomposed(cps).map(unpack_cp);\r\n}\r\nfunction nfc(cps) {\r\n\treturn composed_from_decomposed(decomposed(cps));\r\n}\n\nconst HYPHEN = 0x2D;\r\nconst STOP = 0x2E;\r\nconst STOP_CH = '.';\r\nconst FE0F = 0xFE0F;\r\nconst UNIQUE_PH = 1;\r\n\r\n// 20230913: replace [...v] with Array_from(v) to avoid large spreads\r\nconst Array_from = x => Array.from(x); // Array.from.bind(Array);\r\n\r\nfunction group_has_cp(g, cp) {\r\n\t// 20230913: keep primary and secondary distinct instead of creating valid union\r\n\treturn g.P.has(cp) || g.Q.has(cp);\r\n}\r\n\r\nclass Emoji extends Array {\r\n\tget is_emoji() { return true; } // free tagging system\r\n}\r\n\r\nlet MAPPED, IGNORED, CM, NSM, ESCAPE, NFC_CHECK, GROUPS, WHOLE_VALID, WHOLE_MAP, VALID, EMOJI_LIST, EMOJI_ROOT;\r\n\r\nfunction init() {\r\n\tif (MAPPED) return;\r\n\t\r\n\tlet r = read_compressed_payload(COMPRESSED$1);\r\n\tconst read_sorted_array = () => read_sorted(r);\r\n\tconst read_sorted_set = () => new Set(read_sorted_array());\r\n\tconst set_add_many = (set, v) => v.forEach(x => set.add(x));\r\n\r\n\tMAPPED = new Map(read_mapped(r)); \r\n\tIGNORED = read_sorted_set(); // ignored characters are not valid, so just read raw codepoints\r\n\r\n\t/*\r\n\t// direct include from payload is smaller than the decompression code\r\n\tconst FENCED = new Map(read_array_while(() => {\r\n\t\tlet cp = r();\r\n\t\tif (cp) return [cp, read_str(r())];\r\n\t}));\r\n\t*/\r\n\t// 20230217: we still need all CM for proper error formatting\r\n\t// but norm only needs NSM subset that are potentially-valid\r\n\tCM = read_sorted_array();\r\n\tNSM = new Set(read_sorted_array().map(i => CM[i]));\r\n\tCM = new Set(CM);\r\n\t\r\n\tESCAPE = read_sorted_set(); // characters that should not be printed\r\n\tNFC_CHECK = read_sorted_set(); // only needed to illustrate ens_tokenize() transformations\r\n\r\n\tlet chunks = read_sorted_arrays(r);\r\n\tlet unrestricted = r();\r\n\t//const read_chunked = () => new Set(read_sorted_array().flatMap(i => chunks[i]).concat(read_sorted_array()));\r\n\tconst read_chunked = () => {\r\n\t\t// 20230921: build set in parts, 2x faster\r\n\t\tlet set = new Set();\r\n\t\tread_sorted_array().forEach(i => set_add_many(set, chunks[i]));\r\n\t\tset_add_many(set, read_sorted_array());\r\n\t\treturn set; \r\n\t};\r\n\tGROUPS = read_array_while(i => {\r\n\t\t// minifier property mangling seems unsafe\r\n\t\t// so these are manually renamed to single chars\r\n\t\tlet N = read_array_while(r).map(x => x+0x60);\r\n\t\tif (N.length) {\r\n\t\t\tlet R = i >= unrestricted; // unrestricted then restricted\r\n\t\t\tN[0] -= 32; // capitalize\r\n\t\t\tN = str_from_cps(N);\r\n\t\t\tif (R) N=`Restricted[${N}]`;\r\n\t\t\tlet P = read_chunked(); // primary\r\n\t\t\tlet Q = read_chunked(); // secondary\r\n\t\t\tlet M = !r(); // not-whitelisted, check for NSM\r\n\t\t\t// *** this code currently isn't needed ***\r\n\t\t\t/*\r\n\t\t\tlet V = [...P, ...Q].sort((a, b) => a-b); // derive: sorted valid\r\n\t\t\tlet M = r()-1; // number of combining mark\r\n\t\t\tif (M < 0) { // whitelisted\r\n\t\t\t\tM = new Map(read_array_while(() => {\r\n\t\t\t\t\tlet i = r();\r\n\t\t\t\t\tif (i) return [V[i-1], read_array_while(() => {\r\n\t\t\t\t\t\tlet v = read_array_while(r);\r\n\t\t\t\t\t\tif (v.length) return v.map(x => x-1);\r\n\t\t\t\t\t})];\r\n\t\t\t\t}));\r\n\t\t\t}*/\r\n\t\t\treturn {N, P, Q, M, R};\r\n\t\t}\r\n\t});\r\n\r\n\t// decode compressed wholes\r\n\tWHOLE_VALID = read_sorted_set();\r\n\tWHOLE_MAP = new Map();\r\n\tlet wholes = read_sorted_array().concat(Array_from(WHOLE_VALID)).sort((a, b) => a-b); // must be sorted\r\n\twholes.forEach((cp, i) => {\r\n\t\tlet d = r(); \r\n\t\tlet w = wholes[i] = d ? wholes[i-d] : {V: [], M: new Map()};\r\n\t\tw.V.push(cp); // add to member set\r\n\t\tif (!WHOLE_VALID.has(cp)) {\r\n\t\t\tWHOLE_MAP.set(cp, w);  // register with whole map\r\n\t\t}\r\n\t});\r\n\r\n\t// compute confusable-extent complements\r\n\t// usage: WHOLE_MAP.get(cp).M.get(cp) = complement set\r\n\tfor (let {V, M} of new Set(WHOLE_MAP.values())) {\r\n\t\t// connect all groups that have each whole character\r\n\t\tlet recs = [];\r\n\t\tfor (let cp of V) {\r\n\t\t\tlet gs = GROUPS.filter(g => group_has_cp(g, cp));\r\n\t\t\tlet rec = recs.find(({G}) => gs.some(g => G.has(g)));\r\n\t\t\tif (!rec) {\r\n\t\t\t\trec = {G: new Set(), V: []};\r\n\t\t\t\trecs.push(rec);\r\n\t\t\t}\r\n\t\t\trec.V.push(cp);\r\n\t\t\tset_add_many(rec.G, gs);\r\n\t\t}\r\n\t\t// per character cache groups which are not a member of the extent\r\n\t\tlet union = recs.flatMap(x => Array_from(x.G)); // all of the groups used by this whole\r\n\t\tfor (let {G, V} of recs) {\r\n\t\t\tlet complement = new Set(union.filter(g => !G.has(g))); // groups not covered by the extent\r\n\t\t\tfor (let cp of V) {\r\n\t\t\t\tM.set(cp, complement); // this is the same reference\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// compute valid set\r\n\t// 20230924: VALID was union but can be re-used\r\n\tVALID = new Set(); // exists in 1+ groups\r\n\tlet multi = new Set(); // exists in 2+ groups\r\n\tconst add_to_union = cp => VALID.has(cp) ? multi.add(cp) : VALID.add(cp);\r\n\tfor (let g of GROUPS) {\r\n\t\tfor (let cp of g.P) add_to_union(cp);\r\n\t\tfor (let cp of g.Q) add_to_union(cp);\r\n\t}\r\n\t// dual purpose WHOLE_MAP: return placeholder if unique non-confusable\r\n\tfor (let cp of VALID) {\r\n\t\tif (!WHOLE_MAP.has(cp) && !multi.has(cp)) {\r\n\t\t\tWHOLE_MAP.set(cp, UNIQUE_PH);\r\n\t\t}\r\n\t}\r\n\t// add all decomposed parts\r\n\t// see derive: \"Valid is Closed (via Brute-force)\"\r\n\tset_add_many(VALID, nfd(VALID));\r\n\t\r\n\t// decode emoji\r\n\t// 20230719: emoji are now fully-expanded to avoid quirk logic \r\n\tEMOJI_LIST = read_trie(r).map(v => Emoji.from(v)).sort(compare_arrays);\r\n\tEMOJI_ROOT = new Map(); // this has approx 7K nodes (2+ per emoji)\r\n\tfor (let cps of EMOJI_LIST) {\r\n\t\t// 20230719: change to *slightly* stricter algorithm which disallows \r\n\t\t// insertion of misplaced FE0F in emoji sequences (matching ENSIP-15)\r\n\t\t// example: beautified [A B] (eg. flag emoji) \r\n\t\t//  before: allow: [A FE0F B], error: [A FE0F FE0F B] \r\n\t\t//   after: error: both\r\n\t\t// note: this code now matches ENSNormalize.{cs,java} logic\r\n\t\tlet prev = [EMOJI_ROOT];\r\n\t\tfor (let cp of cps) {\r\n\t\t\tlet next = prev.map(node => {\r\n\t\t\t\tlet child = node.get(cp);\r\n\t\t\t\tif (!child) {\r\n\t\t\t\t\t// should this be object? \r\n\t\t\t\t\t// (most have 1-2 items, few have many)\r\n\t\t\t\t\t// 20230719: no, v8 default map is 4?\r\n\t\t\t\t\tchild = new Map();\r\n\t\t\t\t\tnode.set(cp, child);\r\n\t\t\t\t}\r\n\t\t\t\treturn child;\r\n\t\t\t});\r\n\t\t\tif (cp === FE0F) {\r\n\t\t\t\tprev.push(...next); // less than 20 elements\r\n\t\t\t} else {\r\n\t\t\t\tprev = next;\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor (let x of prev) {\r\n\t\t\tx.V = cps;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// if escaped: {HEX}\r\n//       else: \"x\" {HEX}\r\nfunction quoted_cp(cp) {\r\n\treturn (should_escape(cp) ? '' : `${bidi_qq(safe_str_from_cps([cp]))} `) + quote_cp(cp);\r\n}\r\n\r\n// 20230211: some messages can be mixed-directional and result in spillover\r\n// use 200E after a quoted string to force the remainder of a string from \r\n// acquring the direction of the quote\r\n// https://www.w3.org/International/questions/qa-bidi-unicode-controls#exceptions\r\nfunction bidi_qq(s) {\r\n\treturn `\"${s}\"\\u200E`; // strong LTR\r\n}\r\n\r\nfunction check_label_extension(cps) {\r\n\tif (cps.length >= 4 && cps[2] == HYPHEN && cps[3] == HYPHEN) {\r\n\t\tthrow new Error(`invalid label extension: \"${str_from_cps(cps.slice(0, 4))}\"`); // this can only be ascii so cant be bidi\r\n\t}\r\n}\r\nfunction check_leading_underscore(cps) {\r\n\tconst UNDERSCORE = 0x5F;\r\n\tfor (let i = cps.lastIndexOf(UNDERSCORE); i > 0; ) {\r\n\t\tif (cps[--i] !== UNDERSCORE) {\r\n\t\t\tthrow new Error('underscore allowed only at start');\r\n\t\t}\r\n\t}\r\n}\r\n// check that a fenced cp is not leading, trailing, or touching another fenced cp\r\nfunction check_fenced(cps) {\r\n\tlet cp = cps[0];\r\n\tlet prev = FENCED.get(cp);\r\n\tif (prev) throw error_placement(`leading ${prev}`);\r\n\tlet n = cps.length;\r\n\tlet last = -1; // prevents trailing from throwing\r\n\tfor (let i = 1; i < n; i++) {\r\n\t\tcp = cps[i];\r\n\t\tlet match = FENCED.get(cp);\r\n\t\tif (match) {\r\n\t\t\t// since cps[0] isn't fenced, cps[1] cannot throw\r\n\t\t\tif (last == i) throw error_placement(`${prev} + ${match}`);\r\n\t\t\tlast = i + 1;\r\n\t\t\tprev = match;\r\n\t\t}\r\n\t}\r\n\tif (last == n) throw error_placement(`trailing ${prev}`);\r\n}\r\n\r\n// create a safe to print string \r\n// invisibles are escaped\r\n// leading cm uses placeholder\r\n// if cps exceed max, middle truncate with ellipsis\r\n// quoter(cp) => string, eg. 3000 => \"{3000}\"\r\n// note: in html, you'd call this function then replace [<>&] with entities\r\nfunction safe_str_from_cps(cps, max = Infinity, quoter = quote_cp) {\r\n\t//if (Number.isInteger(cps)) cps = [cps];\r\n\t//if (!Array.isArray(cps)) throw new TypeError(`expected codepoints`);\r\n\tlet buf = [];\r\n\tif (is_combining_mark(cps[0])) buf.push('◌');\r\n\tif (cps.length > max) {\r\n\t\tmax >>= 1;\r\n\t\tcps = [...cps.slice(0, max), 0x2026, ...cps.slice(-max)];\r\n\t}\r\n\tlet prev = 0;\r\n\tlet n = cps.length;\r\n\tfor (let i = 0; i < n; i++) {\r\n\t\tlet cp = cps[i];\r\n\t\tif (should_escape(cp)) {\r\n\t\t\tbuf.push(str_from_cps(cps.slice(prev, i)));\r\n\t\t\tbuf.push(quoter(cp));\r\n\t\t\tprev = i + 1;\r\n\t\t}\r\n\t}\r\n\tbuf.push(str_from_cps(cps.slice(prev, n)));\r\n\treturn buf.join('');\r\n}\r\n\r\n// note: set(s) cannot be exposed because they can be modified\r\n// note: Object.freeze() doesn't work\r\nfunction is_combining_mark(cp) {\r\n\tinit();\r\n\treturn CM.has(cp);\r\n}\r\nfunction should_escape(cp) {\r\n\tinit();\r\n\treturn ESCAPE.has(cp);\r\n}\r\n\r\n// return all supported emoji as fully-qualified emoji \r\n// ordered by length then lexicographic \r\nfunction ens_emoji() {\r\n\tinit();\r\n\treturn EMOJI_LIST.map(x => x.slice()); // emoji are exposed so copy\r\n}\r\n\r\nfunction ens_normalize_fragment(frag, decompose) {\r\n\tinit();\r\n\tlet nf = decompose ? nfd : nfc;\r\n\treturn frag.split(STOP_CH).map(label => str_from_cps(tokens_from_str(explode_cp(label), nf, filter_fe0f).flat())).join(STOP_CH);\r\n}\r\n\r\nfunction ens_normalize(name) {\r\n\treturn flatten(split(name, nfc, filter_fe0f));\r\n}\r\n\r\nfunction ens_beautify(name) {\r\n\tlet labels = split(name, nfc, x => x); // emoji not exposed\r\n\tfor (let {type, output, error} of labels) {\r\n\t\tif (error) break; // flatten will throw\r\n\r\n\t\t// replace leading/trailing hyphen\r\n\t\t// 20230121: consider beautifing all or leading/trailing hyphen to unicode variant\r\n\t\t// not exactly the same in every font, but very similar: \"-\" vs \"‐\"\r\n\t\t/*\r\n\t\tconst UNICODE_HYPHEN = 0x2010;\r\n\t\t// maybe this should replace all for visual consistancy?\r\n\t\t// `node tools/reg-count.js regex ^-\\{2,\\}` => 592\r\n\t\t//for (let i = 0; i < output.length; i++) if (output[i] == 0x2D) output[i] = 0x2010;\r\n\t\tif (output[0] == HYPHEN) output[0] = UNICODE_HYPHEN;\r\n\t\tlet end = output.length-1;\r\n\t\tif (output[end] == HYPHEN) output[end] = UNICODE_HYPHEN;\r\n\t\t*/\r\n\t\t// 20230123: WHATWG URL uses \"CheckHyphens\" false\r\n\t\t// https://url.spec.whatwg.org/#idna\r\n\r\n\t\t// update ethereum symbol\r\n\t\t// ξ => Ξ if not greek\r\n\t\tif (type !== 'Greek') array_replace(output, 0x3BE, 0x39E);\r\n\r\n\t\t// 20221213: fixes bidi subdomain issue, but breaks invariant (200E is disallowed)\r\n\t\t// could be fixed with special case for: 2D (.) + 200E (LTR)\r\n\t\t// https://discuss.ens.domains/t/bidi-label-ordering-spoof/15824\r\n\t\t//output.splice(0, 0, 0x200E);\r\n\t}\r\n\treturn flatten(labels);\r\n}\r\n\r\nfunction array_replace(v, a, b) {\r\n\tlet prev = 0;\r\n\twhile (true) {\r\n\t\tlet next = v.indexOf(a, prev);\r\n\t\tif (next < 0) break;\r\n\t\tv[next] = b; \r\n\t\tprev = next + 1;\r\n\t}\r\n}\r\n\r\nfunction ens_split(name, preserve_emoji) {\r\n\treturn split(name, nfc, preserve_emoji ? x => x.slice() : filter_fe0f); // emoji are exposed so copy\r\n}\r\n\r\nfunction split(name, nf, ef) {\r\n\tif (!name) return []; // 20230719: empty name allowance\r\n\tinit();\r\n\tlet offset = 0;\r\n\t// https://unicode.org/reports/tr46/#Validity_Criteria\r\n\t// 4.) \"The label must not contain a U+002E ( . ) FULL STOP.\"\r\n\treturn name.split(STOP_CH).map(label => {\r\n\t\tlet input = explode_cp(label);\r\n\t\tlet info = {\r\n\t\t\tinput,\r\n\t\t\toffset, // codepoint, not substring!\r\n\t\t};\r\n\t\toffset += input.length + 1; // + stop\r\n\t\ttry {\r\n\t\t\t// 1.) \"The label must be in Unicode Normalization Form NFC\"\r\n\t\t\tlet tokens = info.tokens = tokens_from_str(input, nf, ef);\r\n\t\t\tlet token_count = tokens.length;\r\n\t\t\tlet type;\r\n\t\t\tif (!token_count) { // the label was effectively empty (could of had ignored characters)\r\n\t\t\t\t//norm = [];\r\n\t\t\t\t//type = 'None'; // use this instead of next match, \"ASCII\"\r\n\t\t\t\t// 20230120: change to strict\r\n\t\t\t\t// https://discuss.ens.domains/t/ens-name-normalization-2nd/14564/59\r\n\t\t\t\tthrow new Error(`empty label`);\r\n\t\t\t} \r\n\t\t\tlet norm = info.output = tokens.flat();\r\n\t\t\tcheck_leading_underscore(norm);\r\n\t\t\tlet emoji = info.emoji = token_count > 1 || tokens[0].is_emoji; // same as: tokens.some(x => x.is_emoji);\r\n\t\t\tif (!emoji && norm.every(cp => cp < 0x80)) { // special case for ascii\r\n\t\t\t\t// 20230123: matches matches WHATWG, see note 3.3\r\n\t\t\t\tcheck_label_extension(norm); // only needed for ascii\r\n\t\t\t\t// cant have fenced\r\n\t\t\t\t// cant have cm\r\n\t\t\t\t// cant have wholes\r\n\t\t\t\t// see derive: \"Fastpath ASCII\"\r\n\t\t\t\ttype = 'ASCII';\r\n\t\t\t} else {\r\n\t\t\t\tlet chars = tokens.flatMap(x => x.is_emoji ? [] : x); // all of the nfc tokens concat together\r\n\t\t\t\tif (!chars.length) { // theres no text, just emoji\r\n\t\t\t\t\ttype = 'Emoji';\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 5.) \"The label must not begin with a combining mark, that is: General_Category=Mark.\"\r\n\t\t\t\t\tif (CM.has(norm[0])) throw error_placement('leading combining mark');\r\n\t\t\t\t\tfor (let i = 1; i < token_count; i++) { // we've already checked the first token\r\n\t\t\t\t\t\tlet cps = tokens[i];\r\n\t\t\t\t\t\tif (!cps.is_emoji && CM.has(cps[0])) { // every text token has emoji neighbors, eg. EtEEEtEt...\r\n\t\t\t\t\t\t\t// bidi_qq() not needed since emoji is LTR and cps is a CM\r\n\t\t\t\t\t\t\tthrow error_placement(`emoji + combining mark: \"${str_from_cps(tokens[i-1])} + ${safe_str_from_cps([cps[0]])}\"`); \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcheck_fenced(norm);\r\n\t\t\t\t\tlet unique = Array_from(new Set(chars));\r\n\t\t\t\t\tlet [g] = determine_group(unique); // take the first match\r\n\t\t\t\t\t// see derive: \"Matching Groups have Same CM Style\"\r\n\t\t\t\t\t// alternative: could form a hybrid type: Latin/Japanese/...\t\r\n\t\t\t\t\tcheck_group(g, chars); // need text in order\r\n\t\t\t\t\tcheck_whole(g, unique); // only need unique text (order would be required for multiple-char confusables)\r\n\t\t\t\t\ttype = g.N;\r\n\t\t\t\t\t// 20230121: consider exposing restricted flag\r\n\t\t\t\t\t// it's simpler to just check for 'Restricted'\r\n\t\t\t\t\t// or even better: type.endsWith(']')\r\n\t\t\t\t\t//if (g.R) info.restricted = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tinfo.type = type;\r\n\t\t} catch (err) {\r\n\t\t\tinfo.error = err; // use full error object\r\n\t\t}\r\n\t\treturn info;\r\n\t});\r\n}\r\n\r\nfunction check_whole(group, unique) {\r\n\tlet maker;\r\n\tlet shared = [];\r\n\tfor (let cp of unique) {\r\n\t\tlet whole = WHOLE_MAP.get(cp);\r\n\t\tif (whole === UNIQUE_PH) return; // unique, non-confusable\r\n\t\tif (whole) {\r\n\t\t\tlet set = whole.M.get(cp); // groups which have a character that look-like this character\r\n\t\t\tmaker = maker ? maker.filter(g => set.has(g)) : Array_from(set);\r\n\t\t\tif (!maker.length) return; // confusable intersection is empty\r\n\t\t} else {\r\n\t\t\tshared.push(cp); \r\n\t\t}\r\n\t}\r\n\tif (maker) {\r\n\t\t// we have 1+ confusable\r\n\t\t// check if any of the remaining groups\r\n\t\t// contain the shared characters too\r\n\t\tfor (let g of maker) {\r\n\t\t\tif (shared.every(cp => group_has_cp(g, cp))) {\r\n\t\t\t\tthrow new Error(`whole-script confusable: ${group.N}/${g.N}`);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// assumption: unique.size > 0\r\n// returns list of matching groups\r\nfunction determine_group(unique) {\r\n\tlet groups = GROUPS;\r\n\tfor (let cp of unique) {\r\n\t\t// note: we need to dodge CM that are whitelisted\r\n\t\t// but that code isn't currently necessary\r\n\t\tlet gs = groups.filter(g => group_has_cp(g, cp));\r\n\t\tif (!gs.length) {\r\n\t\t\tif (!GROUPS.some(g => group_has_cp(g, cp))) { \r\n\t\t\t\t// the character was composed of valid parts\r\n\t\t\t\t// but it's NFC form is invalid\r\n\t\t\t\t// 20230716: change to more exact statement, see: ENSNormalize.{cs,java}\r\n\t\t\t\t// note: this doesn't have to be a composition\r\n\t\t\t\t// 20230720: change to full check\r\n\t\t\t\tthrow error_disallowed(cp); // this should be rare\r\n\t\t\t} else {\r\n\t\t\t\t// there is no group that contains all these characters\r\n\t\t\t\t// throw using the highest priority group that matched\r\n\t\t\t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n\t\t\t\tthrow error_group_member(groups[0], cp);\r\n\t\t\t}\r\n\t\t}\r\n\t\tgroups = gs;\r\n\t\tif (gs.length == 1) break; // there is only one group left\r\n\t}\r\n\t// there are at least 1 group(s) with all of these characters\r\n\treturn groups;\r\n}\r\n\r\n// throw on first error\r\nfunction flatten(split) {\r\n\treturn split.map(({input, error, output}) => {\r\n\t\tif (error) {\r\n\t\t\t// don't print label again if just a single label\r\n\t\t\tlet msg = error.message;\r\n\t\t\t// bidi_qq() only necessary if msg is digits\r\n\t\t\tthrow new Error(split.length == 1 ? msg : `Invalid label ${bidi_qq(safe_str_from_cps(input, 63))}: ${msg}`); \r\n\t\t}\r\n\t\treturn str_from_cps(output);\r\n\t}).join(STOP_CH);\r\n}\r\n\r\nfunction error_disallowed(cp) {\r\n\t// TODO: add cp to error?\r\n\treturn new Error(`disallowed character: ${quoted_cp(cp)}`); \r\n}\r\nfunction error_group_member(g, cp) {\r\n\tlet quoted = quoted_cp(cp);\r\n\tlet gg = GROUPS.find(g => g.P.has(cp)); // only check primary\r\n\tif (gg) {\r\n\t\tquoted = `${gg.N} ${quoted}`;\r\n\t}\r\n\treturn new Error(`illegal mixture: ${g.N} + ${quoted}`);\r\n}\r\nfunction error_placement(where) {\r\n\treturn new Error(`illegal placement: ${where}`);\r\n}\r\n\r\n// assumption: cps.length > 0\r\n// assumption: cps[0] isn't a CM\r\n// assumption: the previous character isn't an emoji\r\nfunction check_group(g, cps) {\r\n\tfor (let cp of cps) {\r\n\t\tif (!group_has_cp(g, cp)) {\r\n\t\t\t// for whitelisted scripts, this will throw illegal mixture on invalid cm, eg. \"e{300}{300}\"\r\n\t\t\t// at the moment, it's unnecessary to introduce an extra error type\r\n\t\t\t// until there exists a whitelisted multi-character\r\n\t\t\t//   eg. if (M < 0 && is_combining_mark(cp)) { ... }\r\n\t\t\t// there are 3 cases:\r\n\t\t\t//   1. illegal cm for wrong group => mixture error\r\n\t\t\t//   2. illegal cm for same group => cm error\r\n\t\t\t//       requires set of whitelist cm per group: \r\n\t\t\t//        eg. new Set([...g.P, ...g.Q].flatMap(nfc).filter(cp => CM.has(cp)))\r\n\t\t\t//   3. wrong group => mixture error\r\n\t\t\tthrow error_group_member(g, cp);\r\n\t\t}\r\n\t}\r\n\t//if (M >= 0) { // we have a known fixed cm count\r\n\tif (g.M) { // we need to check for NSM\r\n\t\tlet decomposed = nfd(cps);\r\n\t\tfor (let i = 1, e = decomposed.length; i < e; i++) { // see: assumption\r\n\t\t\t// 20230210: bugfix: using cps instead of decomposed h/t Carbon225\r\n\t\t\t/*\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: ${g.N} ${bidi_qq(str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t\t*/\r\n\t\t\t// 20230217: switch to NSM counting\r\n\t\t\t// https://www.unicode.org/reports/tr39/#Optional_Detection\r\n\t\t\tif (NSM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\tfor (let cp; j < e && NSM.has(cp = decomposed[j]); j++) {\r\n\t\t\t\t\t// a. Forbid sequences of the same nonspacing mark.\r\n\t\t\t\t\tfor (let k = i; k < j; k++) { // O(n^2) but n < 100\r\n\t\t\t\t\t\tif (decomposed[k] == cp) {\r\n\t\t\t\t\t\t\tthrow new Error(`duplicate non-spacing marks: ${quoted_cp(cp)}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// parse to end so we have full nsm count\r\n\t\t\t\t// b. Forbid sequences of more than 4 nonspacing marks (gc=Mn or gc=Me).\r\n\t\t\t\tif (j - i > NSM_MAX) {\r\n\t\t\t\t\t// note: this slice starts with a base char or spacing-mark cm\r\n\t\t\t\t\tthrow new Error(`excessive non-spacing marks: ${bidi_qq(safe_str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${NSM_MAX})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// *** this code currently isn't needed ***\r\n\t/*\r\n\tlet cm_whitelist = M instanceof Map;\r\n\tfor (let i = 0, e = cps.length; i < e; ) {\r\n\t\tlet cp = cps[i++];\r\n\t\tlet seqs = cm_whitelist && M.get(cp);\r\n\t\tif (seqs) { \r\n\t\t\t// list of codepoints that can follow\r\n\t\t\t// if this exists, this will always be 1+\r\n\t\t\tlet j = i;\r\n\t\t\twhile (j < e && CM.has(cps[j])) j++;\r\n\t\t\tlet cms = cps.slice(i, j);\r\n\t\t\tlet match = seqs.find(seq => !compare_arrays(seq, cms));\r\n\t\t\tif (!match) throw new Error(`disallowed combining mark sequence: \"${safe_str_from_cps([cp, ...cms])}\"`);\r\n\t\t\ti = j;\r\n\t\t} else if (!V.has(cp)) {\r\n\t\t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n\t\t\tlet quoted = quoted_cp(cp);\r\n\t\t\tfor (let cp of cps) {\r\n\t\t\t\tlet u = UNIQUE.get(cp);\r\n\t\t\t\tif (u && u !== g) {\r\n\t\t\t\t\t// if both scripts are restricted this error is confusing\r\n\t\t\t\t\t// because we don't differentiate RestrictedA from RestrictedB \r\n\t\t\t\t\tif (!u.R) quoted = `${quoted} is ${u.N}`;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthrow new Error(`disallowed ${g.N} character: ${quoted}`);\r\n\t\t\t//throw new Error(`disallowed character: ${quoted} (expected ${g.N})`);\r\n\t\t\t//throw new Error(`${g.N} does not allow: ${quoted}`);\r\n\t\t}\r\n\t}\r\n\tif (!cm_whitelist) {\r\n\t\tlet decomposed = nfd(cps);\r\n\t\tfor (let i = 1, e = decomposed.length; i < e; i++) { // we know it can't be cm leading\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: \"${str_from_cps(decomposed.slice(i-1, j))}\" (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t*/\r\n}\r\n\r\n// given a list of codepoints\r\n// returns a list of lists, where emoji are a fully-qualified (as Array subclass)\r\n// eg. explode_cp(\"abc💩d\") => [[61, 62, 63], Emoji[1F4A9, FE0F], [64]]\r\n// 20230818: rename for 'process' name collision h/t Javarome\r\n// https://github.com/adraffy/ens-normalize.js/issues/23\r\nfunction tokens_from_str(input, nf, ef) {\r\n\tlet ret = [];\r\n\tlet chars = [];\r\n\tinput = input.slice().reverse(); // flip so we can pop\r\n\twhile (input.length) {\r\n\t\tlet emoji = consume_emoji_reversed(input);\r\n\t\tif (emoji) {\r\n\t\t\tif (chars.length) {\r\n\t\t\t\tret.push(nf(chars));\r\n\t\t\t\tchars = [];\r\n\t\t\t}\r\n\t\t\tret.push(ef(emoji));\r\n\t\t} else {\r\n\t\t\tlet cp = input.pop();\r\n\t\t\tif (VALID.has(cp)) {\r\n\t\t\t\tchars.push(cp);\r\n\t\t\t} else {\r\n\t\t\t\tlet cps = MAPPED.get(cp);\r\n\t\t\t\tif (cps) {\r\n\t\t\t\t\tchars.push(...cps); // less than 10 elements\r\n\t\t\t\t} else if (!IGNORED.has(cp)) {\r\n\t\t\t\t\t// 20230912: unicode 15.1 changed the order of processing such that\r\n\t\t\t\t\t// disallowed parts are only rejected after NFC\r\n\t\t\t\t\t// https://unicode.org/reports/tr46/#Validity_Criteria\r\n\t\t\t\t\t// this doesn't impact normalization as of today\r\n\t\t\t\t\t// technically, this error can be removed as the group logic will apply similar logic\r\n\t\t\t\t\t// however the error type might be less clear\r\n\t\t\t\t\tthrow error_disallowed(cp);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (chars.length) {\r\n\t\tret.push(nf(chars));\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction filter_fe0f(cps) {\r\n\treturn cps.filter(cp => cp != FE0F);\r\n}\r\n\r\n// given array of codepoints\r\n// returns the longest valid emoji sequence (or undefined if no match)\r\n// *MUTATES* the supplied array\r\n// disallows interleaved ignored characters\r\n// fills (optional) eaten array with matched codepoints\r\nfunction consume_emoji_reversed(cps, eaten) {\r\n\tlet node = EMOJI_ROOT;\r\n\tlet emoji;\r\n\tlet pos = cps.length;\r\n\twhile (pos) {\r\n\t\tnode = node.get(cps[--pos]);\r\n\t\tif (!node) break;\r\n\t\tlet {V} = node;\r\n\t\tif (V) { // this is a valid emoji (so far)\r\n\t\t\temoji = V;\r\n\t\t\tif (eaten) eaten.push(...cps.slice(pos).reverse()); // (optional) copy input, used for ens_tokenize()\r\n\t\t\tcps.length = pos; // truncate\r\n\t\t}\r\n\t}\r\n\treturn emoji;\r\n}\r\n\r\n// ************************************************************\r\n// tokenizer \r\n\r\nconst TY_VALID = 'valid';\r\nconst TY_MAPPED = 'mapped';\r\nconst TY_IGNORED = 'ignored';\r\nconst TY_DISALLOWED = 'disallowed';\r\nconst TY_EMOJI = 'emoji';\r\nconst TY_NFC = 'nfc';\r\nconst TY_STOP = 'stop';\r\n\r\nfunction ens_tokenize(name, {\r\n\tnf = true, // collapse unnormalized runs into a single token\r\n} = {}) {\r\n\tinit();\r\n\tlet input = explode_cp(name).reverse();\r\n\tlet eaten = [];\r\n\tlet tokens = [];\r\n\twhile (input.length) {\r\n\t\tlet emoji = consume_emoji_reversed(input, eaten);\r\n\t\tif (emoji) {\r\n\t\t\ttokens.push({\r\n\t\t\t\ttype: TY_EMOJI,\r\n\t\t\t\temoji: emoji.slice(), // copy emoji\r\n\t\t\t\tinput: eaten,\r\n\t\t\t\tcps: filter_fe0f(emoji)\r\n\t\t\t});\r\n\t\t\teaten = []; // reset buffer\r\n\t\t} else {\r\n\t\t\tlet cp = input.pop();\r\n\t\t\tif (cp == STOP) {\r\n\t\t\t\ttokens.push({type: TY_STOP, cp});\r\n\t\t\t} else if (VALID.has(cp)) {\r\n\t\t\t\ttokens.push({type: TY_VALID, cps: [cp]});\r\n\t\t\t} else if (IGNORED.has(cp)) {\r\n\t\t\t\ttokens.push({type: TY_IGNORED, cp});\r\n\t\t\t} else {\r\n\t\t\t\tlet cps = MAPPED.get(cp);\r\n\t\t\t\tif (cps) {\r\n\t\t\t\t\ttokens.push({type: TY_MAPPED, cp, cps: cps.slice()});\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttokens.push({type: TY_DISALLOWED, cp});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (nf) {\r\n\t\tfor (let i = 0, start = -1; i < tokens.length; i++) {\r\n\t\t\tlet token = tokens[i];\r\n\t\t\tif (is_valid_or_mapped(token.type)) {\r\n\t\t\t\tif (requires_check(token.cps)) { // normalization might be needed\r\n\t\t\t\t\tlet end = i + 1;\r\n\t\t\t\t\tfor (let pos = end; pos < tokens.length; pos++) { // find adjacent text\r\n\t\t\t\t\t\tlet {type, cps} = tokens[pos];\r\n\t\t\t\t\t\tif (is_valid_or_mapped(type)) {\r\n\t\t\t\t\t\t\tif (!requires_check(cps)) break;\r\n\t\t\t\t\t\t\tend = pos + 1;\r\n\t\t\t\t\t\t} else if (type !== TY_IGNORED) { // || type !== TY_DISALLOWED) { \r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (start < 0) start = i;\r\n\t\t\t\t\tlet slice = tokens.slice(start, end);\r\n\t\t\t\t\tlet cps0 = slice.flatMap(x => is_valid_or_mapped(x.type) ? x.cps : []); // strip junk tokens\r\n\t\t\t\t\tlet cps = nfc(cps0);\r\n\t\t\t\t\tif (compare_arrays(cps, cps0)) { // bundle into an nfc token\r\n\t\t\t\t\t\ttokens.splice(start, end - start, {\r\n\t\t\t\t\t\t\ttype: TY_NFC, \r\n\t\t\t\t\t\t\tinput: cps0, // there are 3 states: tokens0 ==(process)=> input ==(nfc)=> tokens/cps\r\n\t\t\t\t\t\t\tcps, \r\n\t\t\t\t\t\t\ttokens0: collapse_valid_tokens(slice),\r\n\t\t\t\t\t\t\ttokens: ens_tokenize(str_from_cps(cps), {nf: false})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\ti = start;\r\n\t\t\t\t\t} else { \r\n\t\t\t\t\t\ti = end - 1; // skip to end of slice\r\n\t\t\t\t\t}\r\n\t\t\t\t\tstart = -1; // reset\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstart = i; // remember last\r\n\t\t\t\t}\r\n\t\t\t} else if (token.type !== TY_IGNORED) { // 20221024: is this correct?\r\n\t\t\t\tstart = -1; // reset\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn collapse_valid_tokens(tokens);\r\n}\r\n\r\nfunction is_valid_or_mapped(type) {\r\n\treturn type == TY_VALID || type == TY_MAPPED;\r\n}\r\n\r\nfunction requires_check(cps) {\r\n\treturn cps.some(cp => NFC_CHECK.has(cp));\r\n}\r\n\r\nfunction collapse_valid_tokens(tokens) {\r\n\tfor (let i = 0; i < tokens.length; i++) {\r\n\t\tif (tokens[i].type == TY_VALID) {\r\n\t\t\tlet j = i + 1;\r\n\t\t\twhile (j < tokens.length && tokens[j].type == TY_VALID) j++;\r\n\t\t\ttokens.splice(i, j - i, {type: TY_VALID, cps: tokens.slice(i, j).flatMap(x => x.cps)});\r\n\t\t}\r\n\t}\r\n\treturn tokens;\r\n}\n\nexport { ens_beautify, ens_emoji, ens_normalize, ens_normalize_fragment, ens_split, ens_tokenize, is_combining_mark, nfc, nfd, safe_str_from_cps, should_escape };\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,sDAAsD;AACtD,4EAA4E;AAC5E,4DAA4D;AAC5D,4EAA4E;;;;;;;;;;;;;;AAC5E,IAAI,eAAe;AACnB,MAAM,SAAS,IAAI,IAAI;IAAC;QAAC;QAAK;KAAa;IAAC;QAAC;QAAK;KAAiB;IAAC;QAAC;QAAM;KAAa;CAAC;AACzF,MAAM,UAAU;AAEhB,SAAS,kBAAkB,KAAK;IAC/B,IAAI,MAAM;IACV,SAAS;QAAQ,OAAO,AAAC,KAAK,CAAC,MAAM,IAAI,IAAK,KAAK,CAAC,MAAM;IAAE;IAE5D,6BAA6B;IAC7B,IAAI,eAAe;IACnB,IAAI,QAAQ;IACZ,IAAI,MAAM;QAAC;QAAG;KAAE,EAAE,+BAA+B;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;QACtC,IAAI,IAAI,CAAC,SAAS;IACnB;IAEA,4DAA4D;IAC5D,IAAI,OAAO;IACX,IAAI,cAAc;IAClB,OAAO;IAEP,IAAI,aAAa;IACjB,IAAI,cAAc;IAClB,SAAS;QACR,IAAI,cAAc,GAAG;YACpB,sCAAsC;YACtC,gCAAgC;YAChC,cAAc,AAAC,eAAe,IAAK,KAAK,CAAC,MAAM;YAC/C,aAAa;QACd;QACA,OAAO,AAAC,eAAe,EAAE,aAAc;IACxC;IAEA,MAAM,IAAI;IACV,MAAM,OAAO,KAAG;IAChB,MAAM,OAAO,SAAS;IACtB,MAAM,OAAO,QAAQ;IACrB,MAAM,OAAO,OAAO;IAEpB,gBAAgB;IAChB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK,WAAW,AAAC,YAAY,IAAK;IAEzD,IAAI,UAAU,EAAE;IAChB,IAAI,MAAM;IACV,IAAI,QAAQ,MAAM,qBAAqB;IACvC,MAAO,KAAM;QACZ,IAAI,QAAQ,KAAK,KAAK,CAAC,CAAC,AAAC,CAAC,WAAW,MAAM,CAAC,IAAI,QAAS,CAAC,IAAI;QAC9D,IAAI,QAAQ;QACZ,IAAI,MAAM;QACV,MAAO,MAAM,QAAQ,EAAG;YACvB,IAAI,MAAM,AAAC,QAAQ,QAAS;YAC5B,IAAI,QAAQ,GAAG,CAAC,IAAI,EAAE;gBACrB,MAAM;YACP,OAAO;gBACN,QAAQ;YACT;QACD;QACA,IAAI,SAAS,GAAG,OAAO,2BAA2B;QAClD,QAAQ,IAAI,CAAC;QACb,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,MAAM,GAAK;QAChD,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,QAAM,EAAE,GAAG,SAAS;QACzD,MAAO,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,EAAG;YAC7B,WAAW,AAAC,YAAY,IAAK,OAAO;YACpC,IAAI,AAAC,KAAK,IAAK;YACf,IAAI,AAAC,KAAK,IAAK,OAAO;QACvB;QACA,MAAO,IAAI,CAAC,IAAI,KAAM;YACrB,WAAW,AAAC,WAAW,OAAS,AAAC,YAAY,IAAM,SAAS,IAAM;YAClE,IAAI,AAAC,KAAK,IAAK;YACf,IAAI,AAAC,CAAC,IAAI,IAAI,KAAK,IAAK,OAAO;QAChC;QACA,MAAM;QACN,QAAQ,IAAI,IAAI;IACjB;IACA,IAAI,SAAS,eAAe;IAC5B,OAAO,QAAQ,GAAG,CAAC,CAAA;QAClB,OAAQ,IAAI;YACX,KAAK;gBAAG,OAAO,SAAS,UAAU,CAAC,AAAC,KAAK,CAAC,cAAc,IAAI,KAAO,KAAK,CAAC,cAAc,IAAI,IAAK,KAAK,CAAC,cAAc;YACpH,KAAK;gBAAG,OAAO,SAAS,QAAQ,CAAC,AAAC,KAAK,CAAC,cAAc,IAAI,IAAK,KAAK,CAAC,cAAc;YACnF,KAAK;gBAAG,OAAO,SAAS,KAAK,CAAC,cAAc;YAC5C;gBAAS,OAAO,IAAI;QACrB;IACD;AACD;AAEA,oDAAoD;AACpD,SAAS,aAAa,CAAC;IACtB,IAAI,MAAM;IACV,OAAO,IAAM,CAAC,CAAC,MAAM;AACtB;AACA,SAAS,wBAAwB,CAAC;IACjC,OAAO,aAAa,kBAAkB,YAAY;AACnD;AAEA,uBAAuB;AACvB,2CAA2C;AAC3C,2EAA2E;AAC3E,SAAS,YAAY,CAAC;IACrB,IAAI,SAAS,EAAE;IACf;WAAI;KAAmE,CAAC,OAAO,CAAC,CAAC,GAAG,IAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,GAAG;IACpH,IAAI,IAAI,EAAE,MAAM;IAChB,IAAI,MAAM,IAAI,WAAW,AAAC,IAAI,KAAM;IACpC,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAK;QAC1D,QAAQ,AAAC,SAAS,IAAK,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG;QAC9C,SAAS;QACT,IAAI,SAAS,GAAG;YACf,GAAG,CAAC,MAAM,GAAI,SAAS,CAAC,SAAS,CAAC;QACnC;IACD;IACA,OAAO;AACR;AAEA,sCAAsC;AACtC,SAAS,OAAO,CAAC;IAChB,OAAO,AAAC,IAAI,IAAM,CAAC,KAAK,IAAM,KAAK;AACpC;AAEA,SAAS,YAAY,CAAC,EAAE,IAAI;IAC3B,IAAI,IAAI,MAAM;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,KAAK,OAAO;IACtD,OAAO;AACR;AAEA,gCAAgC;AAChC,SAAS,YAAY,IAAI,EAAE,OAAO,CAAC;IAClC,IAAI,MAAM,EAAE;IACZ,MAAO,KAAM;QACZ,IAAI,IAAI;QACR,IAAI,IAAI;QACR,IAAI,CAAC,GAAG;QACR,QAAQ;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC3B,IAAI,IAAI,CAAC,OAAO;QACjB;QACA,QAAQ,IAAI;IACb;IACA,OAAO;AACR;AAEA,SAAS,mBAAmB,IAAI;IAC/B,OAAO,iBAAiB;QACvB,IAAI,IAAI,YAAY;QACpB,IAAI,EAAE,MAAM,EAAE,OAAO;IACtB;AACD;AAEA,yBAAyB;AACzB,SAAS,YAAY,IAAI;IACxB,IAAI,MAAM,EAAE;IACZ,MAAO,KAAM;QACZ,IAAI,IAAI;QACR,IAAI,KAAK,GAAG;QACZ,IAAI,IAAI,CAAC,kBAAkB,GAAG;IAC/B;IACA,MAAO,KAAM;QACZ,IAAI,IAAI,SAAS;QACjB,IAAI,IAAI,GAAG;QACX,IAAI,IAAI,CAAC,uBAAuB,GAAG;IACpC;IACA,OAAO,IAAI,IAAI;AAChB;AAEA,2BAA2B;AAC3B,8BAA8B;AAC9B,SAAS,iBAAiB,IAAI;IAC7B,IAAI,IAAI,EAAE;IACV,MAAO,KAAM;QACZ,IAAI,IAAI,KAAK,EAAE,MAAM;QACrB,IAAI,CAAC,GAAG;QACR,EAAE,IAAI,CAAC;IACR;IACA,OAAO;AACR;AAEA,6BAA6B;AAC7B,+BAA+B;AAC/B,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,IAAI;IAClC,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,IAAM,EAAE;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC3B,YAAY,GAAG,MAAM,OAAO,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;IAClD;IACA,OAAO;AACR;AAEA,2DAA2D;AAC3D,qDAAqD;AACrD,SAAS,kBAAkB,CAAC,EAAE,IAAI;IACjC,IAAI,KAAK,IAAI;IACb,IAAI,KAAK;IACT,IAAI,KAAK,iBAAiB;IAC1B,IAAI,IAAI,gBAAgB,GAAG,MAAM,EAAE,IAAE,GAAG;IACxC,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG;QACpB,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;QACjB,OAAO,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG;YAClC,IAAI,OAAO,IAAI;YACf,OAAO;gBAAC,IAAI,IAAI;gBAAI,GAAG,GAAG,CAAC,CAAA,IAAK,IAAI;aAAM;QAC3C;IACD;AACD;AAEA,2BAA2B;AAC3B,wBAAwB;AACxB,SAAS,uBAAuB,CAAC,EAAE,IAAI;IACtC,IAAI,IAAI,IAAI;IACZ,IAAI,IAAI,gBAAgB,GAAG,IAAE,GAAG;IAChC,OAAO,EAAE,GAAG,CAAC,CAAA,IAAK;YAAC,CAAC,CAAC,EAAE;YAAE,EAAE,KAAK,CAAC;SAAG;AACrC;AAGA,SAAS,UAAU,IAAI;IACtB,IAAI,MAAM,EAAE;IACZ,IAAI,SAAS,YAAY;IACzB,OAAO,OAAO,EAAE,GAAG,EAAE;IACrB,OAAO,KAAK,aAAa;;;IACzB,SAAS,OAAO,CAAC;QAChB,IAAI,IAAI,QAAQ,4BAA4B;QAC5C,IAAI,IAAI,iBAAiB;YACxB,IAAI,MAAM,YAAY,MAAM,GAAG,CAAC,CAAA,IAAK,MAAM,CAAC,EAAE;YAC9C,IAAI,IAAI,MAAM,EAAE,OAAO,OAAO;QAC/B;QACA,OAAO;YAAC;YAAG;YAAG;QAAC;IAChB;IACA,SAAS,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC,EAAE,GAAG,EAAE,KAAK;QACjC,IAAI,IAAI,KAAK,UAAU,GAAG,CAAC,IAAI,MAAM,GAAC,EAAE,EAAE;QAC1C,IAAI,IAAI,GAAG,QAAQ,GAAG,CAAC,IAAI,MAAM,GAAC,EAAE;QACpC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC;QACpB,KAAK,IAAI,MAAM,EAAG;YACjB,KAAK,IAAI,MAAM,GAAG,CAAC,CAAE;gBACpB,OAAO,IAAI;uBAAI;oBAAK;iBAAG,EAAE;YAC1B;QACD;IACD;AACD;AAEA,SAAS,OAAO,EAAE;IACjB,OAAO,GAAG,QAAQ,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,GAAG;AAClD;AAEA,SAAS,SAAS,EAAE;IACnB,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,8CAA8C;AACzE;AAEA;;;;AAIA,GACA,SAAS,WAAW,CAAC;IACpB,IAAI,MAAM,EAAE;IACZ,IAAK,IAAI,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,MAAM,KAAO;QAC9C,IAAI,KAAK,EAAE,WAAW,CAAC;QACvB,OAAO,KAAK,UAAU,IAAI;QAC1B,IAAI,IAAI,CAAC;IACV;IACA,OAAO;AACR;AAEA,SAAS,aAAa,GAAG;IACxB,MAAM,QAAQ;IACd,IAAI,MAAM,IAAI,MAAM;IACpB,IAAI,MAAM,OAAO,OAAO,OAAO,aAAa,IAAI;IAChD,IAAI,MAAM,EAAE;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAO;QAC1B,IAAI,IAAI,CAAC,OAAO,aAAa,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK;IACpD;IACA,OAAO,IAAI,IAAI,CAAC;AACjB;AAEA,SAAS,eAAe,CAAC,EAAE,CAAC;IAC3B,IAAI,IAAI,EAAE,MAAM;IAChB,IAAI,IAAI,IAAI,EAAE,MAAM;IACpB,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,IAAK,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACrD,OAAO;AACR;AAEA,mCAAmC;AACnC,qDAAqD;AACrD,4EAA4E;AAC5E,4DAA4D;AAC5D,4EAA4E;AAC5E,IAAI,aAAa;AAEjB,oCAAoC;AACpC,+BAA+B;AAC/B,qBAAqB;AAGrB,qBAAqB;AACrB,qEAAqE;AACrE,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,UAAU,UAAU;AAC1B,MAAM,UAAU,UAAU;AAC1B,MAAM,KAAK,KAAK;AAChB,MAAM,KAAK,KAAK;AAChB,MAAM,KAAK,KAAK;AAChB,MAAM,KAAK,KAAK;AAEhB,SAAS,UAAU,MAAM;IACxB,OAAO,AAAC,UAAU,KAAM;AACzB;AACA,SAAS,UAAU,MAAM;IACxB,OAAO,SAAS;AACjB;AAEA,IAAI,cAAc,YAAY,QAAQ;AAEtC,SAAS;IACR,qBAAqB;IACrB,IAAI,IAAI,wBAAwB;IAChC,eAAe,IAAI,IAAI,mBAAmB,GAAG,OAAO,CAAC,CAAC,GAAG,IAAM,EAAE,GAAG,CAAC,CAAA,IAAK;gBAAC;gBAAI,IAAE,KAAM;aAAG,KAAK,cAAc;IAC7G,aAAa,IAAI,IAAI,YAAY;IACjC,SAAS,IAAI;IACb,SAAS,IAAI;IACb,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,YAAY,GAAI;QACrC,IAAI,CAAC,WAAW,GAAG,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;YAC3C,IAAI,CAAC,GAAG,EAAE,GAAG;YACb,IAAI,SAAS,OAAO,GAAG,CAAC;YACxB,IAAI,CAAC,QAAQ;gBACZ,SAAS,IAAI;gBACb,OAAO,GAAG,CAAC,GAAG;YACf;YACA,OAAO,GAAG,CAAC,GAAG;QACf;QACA,OAAO,GAAG,CAAC,IAAI,IAAI,OAAO,KAAK,kBAAkB;IAClD;AACA,wBAAwB;AACxB,iBAAiB;AAClB;AAEA,SAAS,UAAU,EAAE;IACpB,OAAO,MAAM,MAAM,KAAK;AACzB;AAEA,SAAS,aAAa,CAAC,EAAE,CAAC;IACzB,IAAI,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI;QAC3C,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI;IAC7C,OAAO,IAAI,UAAU,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,WAAW,GAAG;QACvE,OAAO,IAAI,CAAC,IAAI,EAAE;IACnB,OAAO;QACN,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,QAAQ;YACX,SAAS,OAAO,GAAG,CAAC;YACpB,IAAI,QAAQ;gBACX,OAAO;YACR;QACD;QACA,OAAO,CAAC;IACT;AACD;AAEA,SAAS,WAAW,GAAG;IACtB,IAAI,CAAC,cAAc;IACnB,IAAI,MAAM,EAAE;IACZ,IAAI,MAAM,EAAE;IACZ,IAAI,cAAc;IAClB,SAAS,IAAI,EAAE;QACd,IAAI,KAAK,aAAa,GAAG,CAAC;QAC1B,IAAI,IAAI;YACP,cAAc;YACd,MAAM;QACP;QACA,IAAI,IAAI,CAAC;IACV;IACA,KAAK,IAAI,MAAM,IAAK;QACnB,MAAO,KAAM;YACZ,IAAI,KAAK,MAAM;gBACd,IAAI,IAAI,CAAC;YACV,OAAO,IAAI,UAAU,KAAK;gBACzB,IAAI,UAAU,KAAK;gBACnB,IAAI,UAAU,UAAU,UAAU;gBAClC,IAAI,UAAU,AAAC,UAAU,UAAW,UAAU;gBAC9C,IAAI,UAAU,UAAU;gBACxB,IAAI,KAAK;gBACT,IAAI,KAAK;gBACT,IAAI,UAAU,GAAG,IAAI,KAAK;YAC3B,OAAO;gBACN,IAAI,SAAS,OAAO,GAAG,CAAC;gBACxB,IAAI,QAAQ;oBACX,IAAI,IAAI,IAAI;gBACb,OAAO;oBACN,IAAI;gBACL;YACD;YACA,IAAI,CAAC,IAAI,MAAM,EAAE;YACjB,KAAK,IAAI,GAAG;QACb;IACD;IACA,IAAI,eAAe,IAAI,MAAM,GAAG,GAAG;QAClC,IAAI,UAAU,UAAU,GAAG,CAAC,EAAE;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACpC,IAAI,KAAK,UAAU,GAAG,CAAC,EAAE;YACzB,IAAI,MAAM,KAAK,WAAW,IAAI;gBAC7B,UAAU;gBACV;YACD;YACA,IAAI,IAAI,IAAE;YACV,MAAO,KAAM;gBACZ,IAAI,MAAM,GAAG,CAAC,IAAE,EAAE;gBAClB,GAAG,CAAC,IAAE,EAAE,GAAG,GAAG,CAAC,EAAE;gBACjB,GAAG,CAAC,EAAE,GAAG;gBACT,IAAI,CAAC,GAAG;gBACR,UAAU,UAAU,GAAG,CAAC,EAAE,EAAE;gBAC5B,IAAI,WAAW,IAAI;YACpB;YACA,UAAU,UAAU,GAAG,CAAC,EAAE;QAC3B;IACD;IACA,OAAO;AACR;AAEA,SAAS,yBAAyB,CAAC;IAClC,IAAI,MAAM,EAAE;IACZ,IAAI,QAAQ,EAAE;IACd,IAAI,UAAU,CAAC;IACf,IAAI,UAAU;IACd,KAAK,IAAI,UAAU,EAAG;QACrB,IAAI,KAAK,UAAU;QACnB,IAAI,KAAK,UAAU;QACnB,IAAI,WAAW,CAAC,GAAG;YAClB,IAAI,MAAM,GAAG;gBACZ,UAAU;YACX,OAAO;gBACN,IAAI,IAAI,CAAC;YACV;QACD,OAAO,IAAI,UAAU,KAAK,WAAW,IAAI;YACxC,IAAI,MAAM,GAAG;gBACZ,IAAI,IAAI,CAAC,YAAY;gBACrB,MAAM,MAAM,GAAG;gBACf,UAAU;YACX,OAAO;gBACN,MAAM,IAAI,CAAC;YACZ;YACA,UAAU;QACX,OAAO;YACN,IAAI,WAAW,aAAa,SAAS;YACrC,IAAI,YAAY,GAAG;gBAClB,UAAU;YACX,OAAO,IAAI,WAAW,KAAK,MAAM,GAAG;gBACnC,IAAI,IAAI,CAAC;gBACT,UAAU;YACX,OAAO;gBACN,MAAM,IAAI,CAAC;gBACX,UAAU;YACX;QACD;IACD;IACA,IAAI,WAAW,GAAG;QACjB,IAAI,IAAI,CAAC,YAAY;IACtB;IACA,OAAO;AACR;AAEA,4BAA4B;AAC5B,SAAS,IAAI,GAAG;IACf,OAAO,WAAW,KAAK,GAAG,CAAC;AAC5B;AACA,SAAS,IAAI,GAAG;IACf,OAAO,yBAAyB,WAAW;AAC5C;AAEA,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,YAAY;AAElB,qEAAqE;AACrE,MAAM,aAAa,CAAA,IAAK,MAAM,IAAI,CAAC,IAAI,0BAA0B;AAEjE,SAAS,aAAa,CAAC,EAAE,EAAE;IAC1B,gFAAgF;IAChF,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC;AAC/B;AAEA,MAAM,cAAc;IACnB,IAAI,WAAW;QAAE,OAAO;IAAM;AAC/B;AAEA,IAAI,QAAQ,SAAS,IAAI,KAAK,QAAQ,WAAW,QAAQ,aAAa,WAAW,OAAO,YAAY;AAEpG,SAAS;IACR,IAAI,QAAQ;IAEZ,IAAI,IAAI,wBAAwB;IAChC,MAAM,oBAAoB,IAAM,YAAY;IAC5C,MAAM,kBAAkB,IAAM,IAAI,IAAI;IACtC,MAAM,eAAe,CAAC,KAAK,IAAM,EAAE,OAAO,CAAC,CAAA,IAAK,IAAI,GAAG,CAAC;IAExD,SAAS,IAAI,IAAI,YAAY;IAC7B,UAAU,mBAAmB,gEAAgE;IAE7F;;;;;;CAMA,GACA,6DAA6D;IAC7D,4DAA4D;IAC5D,KAAK;IACL,MAAM,IAAI,IAAI,oBAAoB,GAAG,CAAC,CAAA,IAAK,EAAE,CAAC,EAAE;IAChD,KAAK,IAAI,IAAI;IAEb,SAAS,mBAAmB,wCAAwC;IACpE,YAAY,mBAAmB,2DAA2D;IAE1F,IAAI,SAAS,mBAAmB;IAChC,IAAI,eAAe;IACnB,8GAA8G;IAC9G,MAAM,eAAe;QACpB,0CAA0C;QAC1C,IAAI,MAAM,IAAI;QACd,oBAAoB,OAAO,CAAC,CAAA,IAAK,aAAa,KAAK,MAAM,CAAC,EAAE;QAC5D,aAAa,KAAK;QAClB,OAAO;IACR;IACA,SAAS,iBAAiB,CAAA;QACzB,0CAA0C;QAC1C,gDAAgD;QAChD,IAAI,IAAI,iBAAiB,GAAG,GAAG,CAAC,CAAA,IAAK,IAAE;QACvC,IAAI,EAAE,MAAM,EAAE;YACb,IAAI,IAAI,KAAK,cAAc,+BAA+B;YAC1D,CAAC,CAAC,EAAE,IAAI,IAAI,aAAa;YACzB,IAAI,aAAa;YACjB,IAAI,GAAG,IAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAC3B,IAAI,IAAI,gBAAgB,UAAU;YAClC,IAAI,IAAI,gBAAgB,YAAY;YACpC,IAAI,IAAI,CAAC,KAAK,iCAAiC;YAC/C,2CAA2C;YAC3C;;;;;;;;;;;IAWC,GACD,OAAO;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;YAAC;QACtB;IACD;IAEA,2BAA2B;IAC3B,cAAc;IACd,YAAY,IAAI;IAChB,IAAI,SAAS,oBAAoB,MAAM,CAAC,WAAW,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,IAAE,IAAI,iBAAiB;IACvG,OAAO,OAAO,CAAC,CAAC,IAAI;QACnB,IAAI,IAAI;QACR,IAAI,IAAI,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,IAAE,EAAE,GAAG;YAAC,GAAG,EAAE;YAAE,GAAG,IAAI;QAAK;QAC1D,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,oBAAoB;QAClC,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK;YACzB,UAAU,GAAG,CAAC,IAAI,IAAK,0BAA0B;QAClD;IACD;IAEA,wCAAwC;IACxC,sDAAsD;IACtD,KAAK,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,IAAI,IAAI,IAAI,UAAU,MAAM,IAAK;QAC/C,oDAAoD;QACpD,IAAI,OAAO,EAAE;QACb,KAAK,IAAI,MAAM,EAAG;YACjB,IAAI,KAAK,OAAO,MAAM,CAAC,CAAA,IAAK,aAAa,GAAG;YAC5C,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,GAAK,GAAG,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC;YAChD,IAAI,CAAC,KAAK;gBACT,MAAM;oBAAC,GAAG,IAAI;oBAAO,GAAG,EAAE;gBAAA;gBAC1B,KAAK,IAAI,CAAC;YACX;YACA,IAAI,CAAC,CAAC,IAAI,CAAC;YACX,aAAa,IAAI,CAAC,EAAE;QACrB;QACA,kEAAkE;QAClE,IAAI,QAAQ,KAAK,OAAO,CAAC,CAAA,IAAK,WAAW,EAAE,CAAC,IAAI,uCAAuC;QACvF,KAAK,IAAI,EAAC,CAAC,EAAE,CAAC,EAAC,IAAI,KAAM;YACxB,IAAI,aAAa,IAAI,IAAI,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,GAAG,CAAC,MAAM,mCAAmC;YAC3F,KAAK,IAAI,MAAM,EAAG;gBACjB,EAAE,GAAG,CAAC,IAAI,aAAa,6BAA6B;YACrD;QACD;IACD;IAEA,oBAAoB;IACpB,+CAA+C;IAC/C,QAAQ,IAAI,OAAO,sBAAsB;IACzC,IAAI,QAAQ,IAAI,OAAO,sBAAsB;IAC7C,MAAM,eAAe,CAAA,KAAM,MAAM,GAAG,CAAC,MAAM,MAAM,GAAG,CAAC,MAAM,MAAM,GAAG,CAAC;IACrE,KAAK,IAAI,KAAK,OAAQ;QACrB,KAAK,IAAI,MAAM,EAAE,CAAC,CAAE,aAAa;QACjC,KAAK,IAAI,MAAM,EAAE,CAAC,CAAE,aAAa;IAClC;IACA,sEAAsE;IACtE,KAAK,IAAI,MAAM,MAAO;QACrB,IAAI,CAAC,UAAU,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,KAAK;YACzC,UAAU,GAAG,CAAC,IAAI;QACnB;IACD;IACA,2BAA2B;IAC3B,kDAAkD;IAClD,aAAa,OAAO,IAAI;IAExB,eAAe;IACf,+DAA+D;IAC/D,aAAa,UAAU,GAAG,GAAG,CAAC,CAAA,IAAK,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC;IACvD,aAAa,IAAI,OAAO,0CAA0C;IAClE,KAAK,IAAI,OAAO,WAAY;QAC3B,qEAAqE;QACrE,qEAAqE;QACrE,8CAA8C;QAC9C,sDAAsD;QACtD,uBAAuB;QACvB,2DAA2D;QAC3D,IAAI,OAAO;YAAC;SAAW;QACvB,KAAK,IAAI,MAAM,IAAK;YACnB,IAAI,OAAO,KAAK,GAAG,CAAC,CAAA;gBACnB,IAAI,QAAQ,KAAK,GAAG,CAAC;gBACrB,IAAI,CAAC,OAAO;oBACX,0BAA0B;oBAC1B,uCAAuC;oBACvC,qCAAqC;oBACrC,QAAQ,IAAI;oBACZ,KAAK,GAAG,CAAC,IAAI;gBACd;gBACA,OAAO;YACR;YACA,IAAI,OAAO,MAAM;gBAChB,KAAK,IAAI,IAAI,OAAO,wBAAwB;YAC7C,OAAO;gBACN,OAAO;YACR;QACD;QACA,KAAK,IAAI,KAAK,KAAM;YACnB,EAAE,CAAC,GAAG;QACP;IACD;AACD;AAEA,oBAAoB;AACpB,wBAAwB;AACxB,SAAS,UAAU,EAAE;IACpB,OAAO,CAAC,cAAc,MAAM,KAAK,GAAG,QAAQ,kBAAkB;QAAC;KAAG,GAAG,CAAC,CAAC,IAAI,SAAS;AACrF;AAEA,2EAA2E;AAC3E,0EAA0E;AAC1E,sCAAsC;AACtC,iFAAiF;AACjF,SAAS,QAAQ,CAAC;IACjB,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,aAAa;AACrC;AAEA,SAAS,sBAAsB,GAAG;IACjC,IAAI,IAAI,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI,UAAU,GAAG,CAAC,EAAE,IAAI,QAAQ;QAC5D,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,aAAa,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,yCAAyC;IAC1H;AACD;AACA,SAAS,yBAAyB,GAAG;IACpC,MAAM,aAAa;IACnB,IAAK,IAAI,IAAI,IAAI,WAAW,CAAC,aAAa,IAAI,GAAK;QAClD,IAAI,GAAG,CAAC,EAAE,EAAE,KAAK,YAAY;YAC5B,MAAM,IAAI,MAAM;QACjB;IACD;AACD;AACA,iFAAiF;AACjF,SAAS,aAAa,GAAG;IACxB,IAAI,KAAK,GAAG,CAAC,EAAE;IACf,IAAI,OAAO,OAAO,GAAG,CAAC;IACtB,IAAI,MAAM,MAAM,gBAAgB,CAAC,QAAQ,EAAE,MAAM;IACjD,IAAI,IAAI,IAAI,MAAM;IAClB,IAAI,OAAO,CAAC,GAAG,kCAAkC;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC3B,KAAK,GAAG,CAAC,EAAE;QACX,IAAI,QAAQ,OAAO,GAAG,CAAC;QACvB,IAAI,OAAO;YACV,iDAAiD;YACjD,IAAI,QAAQ,GAAG,MAAM,gBAAgB,GAAG,KAAK,GAAG,EAAE,OAAO;YACzD,OAAO,IAAI;YACX,OAAO;QACR;IACD;IACA,IAAI,QAAQ,GAAG,MAAM,gBAAgB,CAAC,SAAS,EAAE,MAAM;AACxD;AAEA,iCAAiC;AACjC,yBAAyB;AACzB,8BAA8B;AAC9B,mDAAmD;AACnD,6CAA6C;AAC7C,2EAA2E;AAC3E,SAAS,kBAAkB,GAAG,EAAE,MAAM,QAAQ,EAAE,SAAS,QAAQ;IAChE,yCAAyC;IACzC,sEAAsE;IACtE,IAAI,MAAM,EAAE;IACZ,IAAI,kBAAkB,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC;IACxC,IAAI,IAAI,MAAM,GAAG,KAAK;QACrB,QAAQ;QACR,MAAM;eAAI,IAAI,KAAK,CAAC,GAAG;YAAM;eAAW,IAAI,KAAK,CAAC,CAAC;SAAK;IACzD;IACA,IAAI,OAAO;IACX,IAAI,IAAI,IAAI,MAAM;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC3B,IAAI,KAAK,GAAG,CAAC,EAAE;QACf,IAAI,cAAc,KAAK;YACtB,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM;YACtC,IAAI,IAAI,CAAC,OAAO;YAChB,OAAO,IAAI;QACZ;IACD;IACA,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM;IACtC,OAAO,IAAI,IAAI,CAAC;AACjB;AAEA,8DAA8D;AAC9D,qCAAqC;AACrC,SAAS,kBAAkB,EAAE;IAC5B;IACA,OAAO,GAAG,GAAG,CAAC;AACf;AACA,SAAS,cAAc,EAAE;IACxB;IACA,OAAO,OAAO,GAAG,CAAC;AACnB;AAEA,uDAAuD;AACvD,wCAAwC;AACxC,SAAS;IACR;IACA,OAAO,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,4BAA4B;AACpE;AAEA,SAAS,uBAAuB,IAAI,EAAE,SAAS;IAC9C;IACA,IAAI,KAAK,YAAY,MAAM;IAC3B,OAAO,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,CAAA,QAAS,aAAa,gBAAgB,WAAW,QAAQ,IAAI,aAAa,IAAI,KAAK,IAAI,CAAC;AACxH;AAEA,SAAS,cAAc,IAAI;IAC1B,OAAO,QAAQ,MAAM,MAAM,KAAK;AACjC;AAEA,SAAS,aAAa,IAAI;IACzB,IAAI,SAAS,MAAM,MAAM,KAAK,CAAA,IAAK,IAAI,oBAAoB;IAC3D,KAAK,IAAI,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAC,IAAI,OAAQ;QACzC,IAAI,OAAO,OAAO,qBAAqB;QAEvC,kCAAkC;QAClC,kFAAkF;QAClF,mEAAmE;QACnE;;;;;;;;EAQA,GACA,iDAAiD;QACjD,oCAAoC;QAEpC,yBAAyB;QACzB,sBAAsB;QACtB,IAAI,SAAS,SAAS,cAAc,QAAQ,OAAO;IAEnD,kFAAkF;IAClF,4DAA4D;IAC5D,gEAAgE;IAChE,8BAA8B;IAC/B;IACA,OAAO,QAAQ;AAChB;AAEA,SAAS,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7B,IAAI,OAAO;IACX,MAAO,KAAM;QACZ,IAAI,OAAO,EAAE,OAAO,CAAC,GAAG;QACxB,IAAI,OAAO,GAAG;QACd,CAAC,CAAC,KAAK,GAAG;QACV,OAAO,OAAO;IACf;AACD;AAEA,SAAS,UAAU,IAAI,EAAE,cAAc;IACtC,OAAO,MAAM,MAAM,KAAK,iBAAiB,CAAA,IAAK,EAAE,KAAK,KAAK,cAAc,4BAA4B;AACrG;AAEA,SAAS,MAAM,IAAI,EAAE,EAAE,EAAE,EAAE;IAC1B,IAAI,CAAC,MAAM,OAAO,EAAE,EAAE,iCAAiC;IACvD;IACA,IAAI,SAAS;IACb,sDAAsD;IACtD,6DAA6D;IAC7D,OAAO,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,CAAA;QAC9B,IAAI,QAAQ,WAAW;QACvB,IAAI,OAAO;YACV;YACA;QACD;QACA,UAAU,MAAM,MAAM,GAAG,GAAG,SAAS;QACrC,IAAI;YACH,4DAA4D;YAC5D,IAAI,SAAS,KAAK,MAAM,GAAG,gBAAgB,OAAO,IAAI;YACtD,IAAI,cAAc,OAAO,MAAM;YAC/B,IAAI;YACJ,IAAI,CAAC,aAAa;gBACjB,YAAY;gBACZ,2DAA2D;gBAC3D,6BAA6B;gBAC7B,oEAAoE;gBACpE,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC;YAC9B;YACA,IAAI,OAAO,KAAK,MAAM,GAAG,OAAO,IAAI;YACpC,yBAAyB;YACzB,IAAI,QAAQ,KAAK,KAAK,GAAG,cAAc,KAAK,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,yCAAyC;YACzG,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,CAAA,KAAM,KAAK,OAAO;gBAC1C,iDAAiD;gBACjD,sBAAsB,OAAO,wBAAwB;gBACrD,mBAAmB;gBACnB,eAAe;gBACf,mBAAmB;gBACnB,+BAA+B;gBAC/B,OAAO;YACR,OAAO;gBACN,IAAI,QAAQ,OAAO,OAAO,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,EAAE,GAAG,IAAI,wCAAwC;gBAC9F,IAAI,CAAC,MAAM,MAAM,EAAE;oBAClB,OAAO;gBACR,OAAO;oBACN,wFAAwF;oBACxF,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,gBAAgB;oBAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;wBACrC,IAAI,MAAM,MAAM,CAAC,EAAE;wBACnB,IAAI,CAAC,IAAI,QAAQ,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG;4BACpC,0DAA0D;4BAC1D,MAAM,gBAAgB,CAAC,yBAAyB,EAAE,aAAa,MAAM,CAAC,IAAE,EAAE,EAAE,GAAG,EAAE,kBAAkB;gCAAC,GAAG,CAAC,EAAE;6BAAC,EAAE,CAAC,CAAC;wBAChH;oBACD;oBACA,aAAa;oBACb,IAAI,SAAS,WAAW,IAAI,IAAI;oBAChC,IAAI,CAAC,EAAE,GAAG,gBAAgB,SAAS,uBAAuB;oBAC1D,mDAAmD;oBACnD,6DAA6D;oBAC7D,YAAY,GAAG,QAAQ,qBAAqB;oBAC5C,YAAY,GAAG,SAAS,gFAAgF;oBACxG,OAAO,EAAE,CAAC;gBACV,8CAA8C;gBAC9C,8CAA8C;gBAC9C,qCAAqC;gBACrC,kCAAkC;gBACnC;YACD;YACA,KAAK,IAAI,GAAG;QACb,EAAE,OAAO,KAAK;YACb,KAAK,KAAK,GAAG,KAAK,wBAAwB;QAC3C;QACA,OAAO;IACR;AACD;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM;IACjC,IAAI;IACJ,IAAI,SAAS,EAAE;IACf,KAAK,IAAI,MAAM,OAAQ;QACtB,IAAI,QAAQ,UAAU,GAAG,CAAC;QAC1B,IAAI,UAAU,WAAW,QAAQ,yBAAyB;QAC1D,IAAI,OAAO;YACV,IAAI,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,8DAA8D;YACzF,QAAQ,QAAQ,MAAM,MAAM,CAAC,CAAA,IAAK,IAAI,GAAG,CAAC,MAAM,WAAW;YAC3D,IAAI,CAAC,MAAM,MAAM,EAAE,QAAQ,mCAAmC;QAC/D,OAAO;YACN,OAAO,IAAI,CAAC;QACb;IACD;IACA,IAAI,OAAO;QACV,wBAAwB;QACxB,uCAAuC;QACvC,oCAAoC;QACpC,KAAK,IAAI,KAAK,MAAO;YACpB,IAAI,OAAO,KAAK,CAAC,CAAA,KAAM,aAAa,GAAG,MAAM;gBAC5C,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;YAC7D;QACD;IACD;AACD;AAEA,8BAA8B;AAC9B,kCAAkC;AAClC,SAAS,gBAAgB,MAAM;IAC9B,IAAI,SAAS;IACb,KAAK,IAAI,MAAM,OAAQ;QACtB,iDAAiD;QACjD,0CAA0C;QAC1C,IAAI,KAAK,OAAO,MAAM,CAAC,CAAA,IAAK,aAAa,GAAG;QAC5C,IAAI,CAAC,GAAG,MAAM,EAAE;YACf,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA,IAAK,aAAa,GAAG,MAAM;gBAC3C,4CAA4C;gBAC5C,+BAA+B;gBAC/B,wEAAwE;gBACxE,8CAA8C;gBAC9C,iCAAiC;gBACjC,MAAM,iBAAiB,KAAK,sBAAsB;YACnD,OAAO;gBACN,uDAAuD;gBACvD,sDAAsD;gBACtD,iEAAiE;gBACjE,MAAM,mBAAmB,MAAM,CAAC,EAAE,EAAE;YACrC;QACD;QACA,SAAS;QACT,IAAI,GAAG,MAAM,IAAI,GAAG,OAAO,+BAA+B;IAC3D;IACA,6DAA6D;IAC7D,OAAO;AACR;AAEA,uBAAuB;AACvB,SAAS,QAAQ,KAAK;IACrB,OAAO,MAAM,GAAG,CAAC,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAC;QACvC,IAAI,OAAO;YACV,iDAAiD;YACjD,IAAI,MAAM,MAAM,OAAO;YACvB,4CAA4C;YAC5C,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,QAAQ,kBAAkB,OAAO,KAAK,EAAE,EAAE,KAAK;QAC3G;QACA,OAAO,aAAa;IACrB,GAAG,IAAI,CAAC;AACT;AAEA,SAAS,iBAAiB,EAAE;IAC3B,yBAAyB;IACzB,OAAO,IAAI,MAAM,CAAC,sBAAsB,EAAE,UAAU,KAAK;AAC1D;AACA,SAAS,mBAAmB,CAAC,EAAE,EAAE;IAChC,IAAI,SAAS,UAAU;IACvB,IAAI,KAAK,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,qBAAqB;IAC7D,IAAI,IAAI;QACP,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ;IAC7B;IACA,OAAO,IAAI,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,QAAQ;AACvD;AACA,SAAS,gBAAgB,KAAK;IAC7B,OAAO,IAAI,MAAM,CAAC,mBAAmB,EAAE,OAAO;AAC/C;AAEA,6BAA6B;AAC7B,gCAAgC;AAChC,oDAAoD;AACpD,SAAS,YAAY,CAAC,EAAE,GAAG;IAC1B,KAAK,IAAI,MAAM,IAAK;QACnB,IAAI,CAAC,aAAa,GAAG,KAAK;YACzB,4FAA4F;YAC5F,mEAAmE;YACnE,mDAAmD;YACnD,oDAAoD;YACpD,qBAAqB;YACrB,mDAAmD;YACnD,6CAA6C;YAC7C,iDAAiD;YACjD,6EAA6E;YAC7E,oCAAoC;YACpC,MAAM,mBAAmB,GAAG;QAC7B;IACD;IACA,iDAAiD;IACjD,IAAI,EAAE,CAAC,EAAE;QACR,IAAI,aAAa,IAAI;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAI,GAAG,IAAK;YAClD,kEAAkE;YAClE;;;;;;;;;GASA,GACA,mCAAmC;YACnC,2DAA2D;YAC3D,IAAI,IAAI,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG;gBAC3B,IAAI,IAAI,IAAI;gBACZ,IAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC,KAAK,UAAU,CAAC,EAAE,GAAG,IAAK;oBACvD,mDAAmD;oBACnD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;wBAC3B,IAAI,UAAU,CAAC,EAAE,IAAI,IAAI;4BACxB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,UAAU,KAAK;wBAChE;oBACD;gBACD;gBACA,yCAAyC;gBACzC,wEAAwE;gBACxE,IAAI,IAAI,IAAI,SAAS;oBACpB,8DAA8D;oBAC9D,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,QAAQ,kBAAkB,WAAW,KAAK,CAAC,IAAE,GAAG,KAAK,EAAE,EAAE,IAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC3H;gBACA,IAAI;YACL;QACD;IACD;AACA,2CAA2C;AAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4CA,GACD;AAEA,6BAA6B;AAC7B,iFAAiF;AACjF,uEAAuE;AACvE,6DAA6D;AAC7D,wDAAwD;AACxD,SAAS,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE;IACrC,IAAI,MAAM,EAAE;IACZ,IAAI,QAAQ,EAAE;IACd,QAAQ,MAAM,KAAK,GAAG,OAAO,IAAI,qBAAqB;IACtD,MAAO,MAAM,MAAM,CAAE;QACpB,IAAI,QAAQ,uBAAuB;QACnC,IAAI,OAAO;YACV,IAAI,MAAM,MAAM,EAAE;gBACjB,IAAI,IAAI,CAAC,GAAG;gBACZ,QAAQ,EAAE;YACX;YACA,IAAI,IAAI,CAAC,GAAG;QACb,OAAO;YACN,IAAI,KAAK,MAAM,GAAG;YAClB,IAAI,MAAM,GAAG,CAAC,KAAK;gBAClB,MAAM,IAAI,CAAC;YACZ,OAAO;gBACN,IAAI,MAAM,OAAO,GAAG,CAAC;gBACrB,IAAI,KAAK;oBACR,MAAM,IAAI,IAAI,MAAM,wBAAwB;gBAC7C,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK;oBAC5B,mEAAmE;oBACnE,+CAA+C;oBAC/C,sDAAsD;oBACtD,gDAAgD;oBAChD,qFAAqF;oBACrF,6CAA6C;oBAC7C,MAAM,iBAAiB;gBACxB;YACD;QACD;IACD;IACA,IAAI,MAAM,MAAM,EAAE;QACjB,IAAI,IAAI,CAAC,GAAG;IACb;IACA,OAAO;AACR;AAEA,SAAS,YAAY,GAAG;IACvB,OAAO,IAAI,MAAM,CAAC,CAAA,KAAM,MAAM;AAC/B;AAEA,4BAA4B;AAC5B,sEAAsE;AACtE,+BAA+B;AAC/B,2CAA2C;AAC3C,uDAAuD;AACvD,SAAS,uBAAuB,GAAG,EAAE,KAAK;IACzC,IAAI,OAAO;IACX,IAAI;IACJ,IAAI,MAAM,IAAI,MAAM;IACpB,MAAO,IAAK;QACX,OAAO,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI;QAC1B,IAAI,CAAC,MAAM;QACX,IAAI,EAAC,CAAC,EAAC,GAAG;QACV,IAAI,GAAG;YACN,QAAQ;YACR,IAAI,OAAO,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC,KAAK,OAAO,KAAK,iDAAiD;YACrG,IAAI,MAAM,GAAG,KAAK,WAAW;QAC9B;IACD;IACA,OAAO;AACR;AAEA,+DAA+D;AAC/D,aAAa;AAEb,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,MAAM,gBAAgB;AACtB,MAAM,WAAW;AACjB,MAAM,SAAS;AACf,MAAM,UAAU;AAEhB,SAAS,aAAa,IAAI,EAAE,EAC3B,KAAK,IAAI,EACT,GAAG,CAAC,CAAC;IACL;IACA,IAAI,QAAQ,WAAW,MAAM,OAAO;IACpC,IAAI,QAAQ,EAAE;IACd,IAAI,SAAS,EAAE;IACf,MAAO,MAAM,MAAM,CAAE;QACpB,IAAI,QAAQ,uBAAuB,OAAO;QAC1C,IAAI,OAAO;YACV,OAAO,IAAI,CAAC;gBACX,MAAM;gBACN,OAAO,MAAM,KAAK;gBAClB,OAAO;gBACP,KAAK,YAAY;YAClB;YACA,QAAQ,EAAE,EAAE,eAAe;QAC5B,OAAO;YACN,IAAI,KAAK,MAAM,GAAG;YAClB,IAAI,MAAM,MAAM;gBACf,OAAO,IAAI,CAAC;oBAAC,MAAM;oBAAS;gBAAE;YAC/B,OAAO,IAAI,MAAM,GAAG,CAAC,KAAK;gBACzB,OAAO,IAAI,CAAC;oBAAC,MAAM;oBAAU,KAAK;wBAAC;qBAAG;gBAAA;YACvC,OAAO,IAAI,QAAQ,GAAG,CAAC,KAAK;gBAC3B,OAAO,IAAI,CAAC;oBAAC,MAAM;oBAAY;gBAAE;YAClC,OAAO;gBACN,IAAI,MAAM,OAAO,GAAG,CAAC;gBACrB,IAAI,KAAK;oBACR,OAAO,IAAI,CAAC;wBAAC,MAAM;wBAAW;wBAAI,KAAK,IAAI,KAAK;oBAAE;gBACnD,OAAO;oBACN,OAAO,IAAI,CAAC;wBAAC,MAAM;wBAAe;oBAAE;gBACrC;YACD;QACD;IACD;IACA,IAAI,IAAI;QACP,IAAK,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACnD,IAAI,QAAQ,MAAM,CAAC,EAAE;YACrB,IAAI,mBAAmB,MAAM,IAAI,GAAG;gBACnC,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC9B,IAAI,MAAM,IAAI;oBACd,IAAK,IAAI,MAAM,KAAK,MAAM,OAAO,MAAM,EAAE,MAAO;wBAC/C,IAAI,EAAC,IAAI,EAAE,GAAG,EAAC,GAAG,MAAM,CAAC,IAAI;wBAC7B,IAAI,mBAAmB,OAAO;4BAC7B,IAAI,CAAC,eAAe,MAAM;4BAC1B,MAAM,MAAM;wBACb,OAAO,IAAI,SAAS,YAAY;4BAC/B;wBACD;oBACD;oBACA,IAAI,QAAQ,GAAG,QAAQ;oBACvB,IAAI,QAAQ,OAAO,KAAK,CAAC,OAAO;oBAChC,IAAI,OAAO,MAAM,OAAO,CAAC,CAAA,IAAK,mBAAmB,EAAE,IAAI,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,oBAAoB;oBAC5F,IAAI,MAAM,IAAI;oBACd,IAAI,eAAe,KAAK,OAAO;wBAC9B,OAAO,MAAM,CAAC,OAAO,MAAM,OAAO;4BACjC,MAAM;4BACN,OAAO;4BACP;4BACA,SAAS,sBAAsB;4BAC/B,QAAQ,aAAa,aAAa,MAAM;gCAAC,IAAI;4BAAK;wBACnD;wBACA,IAAI;oBACL,OAAO;wBACN,IAAI,MAAM,GAAG,uBAAuB;oBACrC;oBACA,QAAQ,CAAC,GAAG,QAAQ;gBACrB,OAAO;oBACN,QAAQ,GAAG,gBAAgB;gBAC5B;YACD,OAAO,IAAI,MAAM,IAAI,KAAK,YAAY;gBACrC,QAAQ,CAAC,GAAG,QAAQ;YACrB;QACD;IACD;IACA,OAAO,sBAAsB;AAC9B;AAEA,SAAS,mBAAmB,IAAI;IAC/B,OAAO,QAAQ,YAAY,QAAQ;AACpC;AAEA,SAAS,eAAe,GAAG;IAC1B,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,UAAU,GAAG,CAAC;AACrC;AAEA,SAAS,sBAAsB,MAAM;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACvC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,UAAU;YAC/B,IAAI,IAAI,IAAI;YACZ,MAAO,IAAI,OAAO,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,SAAU;YACxD,OAAO,MAAM,CAAC,GAAG,IAAI,GAAG;gBAAC,MAAM;gBAAU,KAAK,OAAO,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA,IAAK,EAAE,GAAG;YAAC;QACrF;IACD;IACA,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2771, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/constants.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  BINARY_TYPES: ['nodebuffer', 'arraybuffer', 'fragments'],\n  EMPTY_BUFFER: Buffer.alloc(0),\n  GUID: '258EAFA5-E914-47DA-95CA-C5AB0DC85B11',\n  kForOnEventAttribute: Symbol('kIsForOnEventAttribute'),\n  kListener: Symbol('kListener'),\n  kStatusCode: Symbol('status-code'),\n  kWebSocket: Symbol('websocket'),\n  NOOP: () => {}\n};\n"], "names": [], "mappings": "AAEA,OAAO,OAAO,GAAG;IACf,cAAc;QAAC;QAAc;QAAe;KAAY;IACxD,cAAc,OAAO,KAAK,CAAC;IAC3B,MAAM;IACN,sBAAsB,OAAO;IAC7B,WAAW,OAAO;IAClB,aAAa,OAAO;IACpB,YAAY,OAAO;IACnB,MAAM,KAAO;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2791, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/buffer-util.js"], "sourcesContent": ["'use strict';\n\nconst { EMPTY_BUFFER } = require('./constants');\n\nconst FastBuffer = Buffer[Symbol.species];\n\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {<PERSON>uffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */\nfunction concat(list, totalLength) {\n  if (list.length === 0) return EMPTY_BUFFER;\n  if (list.length === 1) return list[0];\n\n  const target = Buffer.allocUnsafe(totalLength);\n  let offset = 0;\n\n  for (let i = 0; i < list.length; i++) {\n    const buf = list[i];\n    target.set(buf, offset);\n    offset += buf.length;\n  }\n\n  if (offset < totalLength) {\n    return new FastBuffer(target.buffer, target.byteOffset, offset);\n  }\n\n  return target;\n}\n\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {<PERSON>uffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */\nfunction _mask(source, mask, output, offset, length) {\n  for (let i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask[i & 3];\n  }\n}\n\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */\nfunction _unmask(buffer, mask) {\n  for (let i = 0; i < buffer.length; i++) {\n    buffer[i] ^= mask[i & 3];\n  }\n}\n\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */\nfunction toArrayBuffer(buf) {\n  if (buf.length === buf.buffer.byteLength) {\n    return buf.buffer;\n  }\n\n  return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */\nfunction toBuffer(data) {\n  toBuffer.readOnly = true;\n\n  if (Buffer.isBuffer(data)) return data;\n\n  let buf;\n\n  if (data instanceof ArrayBuffer) {\n    buf = new FastBuffer(data);\n  } else if (ArrayBuffer.isView(data)) {\n    buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n  } else {\n    buf = Buffer.from(data);\n    toBuffer.readOnly = false;\n  }\n\n  return buf;\n}\n\nmodule.exports = {\n  concat,\n  mask: _mask,\n  toArrayBuffer,\n  toBuffer,\n  unmask: _unmask\n};\n\n/* istanbul ignore else  */\nif (!process.env.WS_NO_BUFFER_UTIL) {\n  try {\n    const bufferUtil = require('bufferutil');\n\n    module.exports.mask = function (source, mask, output, offset, length) {\n      if (length < 48) _mask(source, mask, output, offset, length);\n      else bufferUtil.mask(source, mask, output, offset, length);\n    };\n\n    module.exports.unmask = function (buffer, mask) {\n      if (buffer.length < 32) _unmask(buffer, mask);\n      else bufferUtil.unmask(buffer, mask);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,YAAY,EAAE;AAEtB,MAAM,aAAa,MAAM,CAAC,OAAO,OAAO,CAAC;AAEzC;;;;;;;CAOC,GACD,SAAS,OAAO,IAAI,EAAE,WAAW;IAC/B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;IAC9B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,IAAI,CAAC,EAAE;IAErC,MAAM,SAAS,OAAO,WAAW,CAAC;IAClC,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,CAAC,KAAK;QAChB,UAAU,IAAI,MAAM;IACtB;IAEA,IAAI,SAAS,aAAa;QACxB,OAAO,IAAI,WAAW,OAAO,MAAM,EAAE,OAAO,UAAU,EAAE;IAC1D;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,MAAM,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;IAC9C;AACF;AAEA;;;;;;CAMC,GACD,SAAS,QAAQ,MAAM,EAAE,IAAI;IAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;IAC1B;AACF;AAEA;;;;;;CAMC,GACD,SAAS,cAAc,GAAG;IACxB,IAAI,IAAI,MAAM,KAAK,IAAI,MAAM,CAAC,UAAU,EAAE;QACxC,OAAO,IAAI,MAAM;IACnB;IAEA,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,UAAU,EAAE,IAAI,UAAU,GAAG,IAAI,MAAM;AACrE;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,IAAI;IACpB,SAAS,QAAQ,GAAG;IAEpB,IAAI,OAAO,QAAQ,CAAC,OAAO,OAAO;IAElC,IAAI;IAEJ,IAAI,gBAAgB,aAAa;QAC/B,MAAM,IAAI,WAAW;IACvB,OAAO,IAAI,YAAY,MAAM,CAAC,OAAO;QACnC,MAAM,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU;IACpE,OAAO;QACL,MAAM,OAAO,IAAI,CAAC;QAClB,SAAS,QAAQ,GAAG;IACtB;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG;IACf;IACA,MAAM;IACN;IACA;IACA,QAAQ;AACV;AAEA,yBAAyB,GACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,IAAI;QACF,MAAM;;;;;QAEN,OAAO,OAAO,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAClE,IAAI,SAAS,IAAI,MAAM,QAAQ,MAAM,QAAQ,QAAQ;iBAChD,WAAW,IAAI,CAAC,QAAQ,MAAM,QAAQ,QAAQ;QACrD;QAEA,OAAO,OAAO,CAAC,MAAM,GAAG,SAAU,MAAM,EAAE,IAAI;YAC5C,IAAI,OAAO,MAAM,GAAG,IAAI,QAAQ,QAAQ;iBACnC,WAAW,MAAM,CAAC,QAAQ;QACjC;IACF,EAAE,OAAO,GAAG;IACV,oCAAoC;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2905, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/limiter.js"], "sourcesContent": ["'use strict';\n\nconst kDone = Symbol('kDone');\nconst kRun = Symbol('kRun');\n\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */\nclass Limiter {\n  /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */\n  constructor(concurrency) {\n    this[kDone] = () => {\n      this.pending--;\n      this[kRun]();\n    };\n    this.concurrency = concurrency || Infinity;\n    this.jobs = [];\n    this.pending = 0;\n  }\n\n  /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */\n  add(job) {\n    this.jobs.push(job);\n    this[kRun]();\n  }\n\n  /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */\n  [kRun]() {\n    if (this.pending === this.concurrency) return;\n\n    if (this.jobs.length) {\n      const job = this.jobs.shift();\n\n      this.pending++;\n      job(this[kDone]);\n    }\n  }\n}\n\nmodule.exports = Limiter;\n"], "names": [], "mappings": "AAEA,MAAM,QAAQ,OAAO;AACrB,MAAM,OAAO,OAAO;AAEpB;;;CAGC,GACD,MAAM;IACJ;;;;;GAKC,GACD,YAAY,WAAW,CAAE;QACvB,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,KAAK;QACZ;QACA,IAAI,CAAC,WAAW,GAAG,eAAe;QAClC,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;;;;GAKC,GACD,IAAI,GAAG,EAAE;QACP,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,KAAK;IACZ;IAEA;;;;GAIC,GACD,CAAC,KAAK,GAAG;QACP,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,WAAW,EAAE;QAEvC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK;YAE3B,IAAI,CAAC,OAAO;YACZ,IAAI,IAAI,CAAC,MAAM;QACjB;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2954, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/permessage-deflate.js"], "sourcesContent": ["'use strict';\n\nconst zlib = require('zlib');\n\nconst bufferUtil = require('./buffer-util');\nconst Limiter = require('./limiter');\nconst { kStatusCode } = require('./constants');\n\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([0x00, 0x00, 0xff, 0xff]);\nconst kPerMessageDeflate = Symbol('permessage-deflate');\nconst kTotalLength = Symbol('total-length');\nconst kCallback = Symbol('callback');\nconst kBuffers = Symbol('buffers');\nconst kError = Symbol('error');\n\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n\n/**\n * permessage-deflate implementation.\n */\nclass PerMessageDeflate {\n  /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */\n  constructor(options, isServer, maxPayload) {\n    this._maxPayload = maxPayload | 0;\n    this._options = options || {};\n    this._threshold =\n      this._options.threshold !== undefined ? this._options.threshold : 1024;\n    this._isServer = !!isServer;\n    this._deflate = null;\n    this._inflate = null;\n\n    this.params = null;\n\n    if (!zlibLimiter) {\n      const concurrency =\n        this._options.concurrencyLimit !== undefined\n          ? this._options.concurrencyLimit\n          : 10;\n      zlibLimiter = new Limiter(concurrency);\n    }\n  }\n\n  /**\n   * @type {String}\n   */\n  static get extensionName() {\n    return 'permessage-deflate';\n  }\n\n  /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */\n  offer() {\n    const params = {};\n\n    if (this._options.serverNoContextTakeover) {\n      params.server_no_context_takeover = true;\n    }\n    if (this._options.clientNoContextTakeover) {\n      params.client_no_context_takeover = true;\n    }\n    if (this._options.serverMaxWindowBits) {\n      params.server_max_window_bits = this._options.serverMaxWindowBits;\n    }\n    if (this._options.clientMaxWindowBits) {\n      params.client_max_window_bits = this._options.clientMaxWindowBits;\n    } else if (this._options.clientMaxWindowBits == null) {\n      params.client_max_window_bits = true;\n    }\n\n    return params;\n  }\n\n  /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */\n  accept(configurations) {\n    configurations = this.normalizeParams(configurations);\n\n    this.params = this._isServer\n      ? this.acceptAsServer(configurations)\n      : this.acceptAsClient(configurations);\n\n    return this.params;\n  }\n\n  /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */\n  cleanup() {\n    if (this._inflate) {\n      this._inflate.close();\n      this._inflate = null;\n    }\n\n    if (this._deflate) {\n      const callback = this._deflate[kCallback];\n\n      this._deflate.close();\n      this._deflate = null;\n\n      if (callback) {\n        callback(\n          new Error(\n            'The deflate stream was closed while data was being processed'\n          )\n        );\n      }\n    }\n  }\n\n  /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsServer(offers) {\n    const opts = this._options;\n    const accepted = offers.find((params) => {\n      if (\n        (opts.serverNoContextTakeover === false &&\n          params.server_no_context_takeover) ||\n        (params.server_max_window_bits &&\n          (opts.serverMaxWindowBits === false ||\n            (typeof opts.serverMaxWindowBits === 'number' &&\n              opts.serverMaxWindowBits > params.server_max_window_bits))) ||\n        (typeof opts.clientMaxWindowBits === 'number' &&\n          !params.client_max_window_bits)\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n\n    if (!accepted) {\n      throw new Error('None of the extension offers can be accepted');\n    }\n\n    if (opts.serverNoContextTakeover) {\n      accepted.server_no_context_takeover = true;\n    }\n    if (opts.clientNoContextTakeover) {\n      accepted.client_no_context_takeover = true;\n    }\n    if (typeof opts.serverMaxWindowBits === 'number') {\n      accepted.server_max_window_bits = opts.serverMaxWindowBits;\n    }\n    if (typeof opts.clientMaxWindowBits === 'number') {\n      accepted.client_max_window_bits = opts.clientMaxWindowBits;\n    } else if (\n      accepted.client_max_window_bits === true ||\n      opts.clientMaxWindowBits === false\n    ) {\n      delete accepted.client_max_window_bits;\n    }\n\n    return accepted;\n  }\n\n  /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsClient(response) {\n    const params = response[0];\n\n    if (\n      this._options.clientNoContextTakeover === false &&\n      params.client_no_context_takeover\n    ) {\n      throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n    }\n\n    if (!params.client_max_window_bits) {\n      if (typeof this._options.clientMaxWindowBits === 'number') {\n        params.client_max_window_bits = this._options.clientMaxWindowBits;\n      }\n    } else if (\n      this._options.clientMaxWindowBits === false ||\n      (typeof this._options.clientMaxWindowBits === 'number' &&\n        params.client_max_window_bits > this._options.clientMaxWindowBits)\n    ) {\n      throw new Error(\n        'Unexpected or invalid parameter \"client_max_window_bits\"'\n      );\n    }\n\n    return params;\n  }\n\n  /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */\n  normalizeParams(configurations) {\n    configurations.forEach((params) => {\n      Object.keys(params).forEach((key) => {\n        let value = params[key];\n\n        if (value.length > 1) {\n          throw new Error(`Parameter \"${key}\" must have only a single value`);\n        }\n\n        value = value[0];\n\n        if (key === 'client_max_window_bits') {\n          if (value !== true) {\n            const num = +value;\n            if (!Number.isInteger(num) || num < 8 || num > 15) {\n              throw new TypeError(\n                `Invalid value for parameter \"${key}\": ${value}`\n              );\n            }\n            value = num;\n          } else if (!this._isServer) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else if (key === 'server_max_window_bits') {\n          const num = +value;\n          if (!Number.isInteger(num) || num < 8 || num > 15) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n          value = num;\n        } else if (\n          key === 'client_no_context_takeover' ||\n          key === 'server_no_context_takeover'\n        ) {\n          if (value !== true) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else {\n          throw new Error(`Unknown parameter \"${key}\"`);\n        }\n\n        params[key] = value;\n      });\n    });\n\n    return configurations;\n  }\n\n  /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  decompress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._decompress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  compress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._compress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _decompress(data, fin, callback) {\n    const endpoint = this._isServer ? 'client' : 'server';\n\n    if (!this._inflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._inflate = zlib.createInflateRaw({\n        ...this._options.zlibInflateOptions,\n        windowBits\n      });\n      this._inflate[kPerMessageDeflate] = this;\n      this._inflate[kTotalLength] = 0;\n      this._inflate[kBuffers] = [];\n      this._inflate.on('error', inflateOnError);\n      this._inflate.on('data', inflateOnData);\n    }\n\n    this._inflate[kCallback] = callback;\n\n    this._inflate.write(data);\n    if (fin) this._inflate.write(TRAILER);\n\n    this._inflate.flush(() => {\n      const err = this._inflate[kError];\n\n      if (err) {\n        this._inflate.close();\n        this._inflate = null;\n        callback(err);\n        return;\n      }\n\n      const data = bufferUtil.concat(\n        this._inflate[kBuffers],\n        this._inflate[kTotalLength]\n      );\n\n      if (this._inflate._readableState.endEmitted) {\n        this._inflate.close();\n        this._inflate = null;\n      } else {\n        this._inflate[kTotalLength] = 0;\n        this._inflate[kBuffers] = [];\n\n        if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n          this._inflate.reset();\n        }\n      }\n\n      callback(null, data);\n    });\n  }\n\n  /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _compress(data, fin, callback) {\n    const endpoint = this._isServer ? 'server' : 'client';\n\n    if (!this._deflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._deflate = zlib.createDeflateRaw({\n        ...this._options.zlibDeflateOptions,\n        windowBits\n      });\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      this._deflate.on('data', deflateOnData);\n    }\n\n    this._deflate[kCallback] = callback;\n\n    this._deflate.write(data);\n    this._deflate.flush(zlib.Z_SYNC_FLUSH, () => {\n      if (!this._deflate) {\n        //\n        // The deflate stream was closed while data was being processed.\n        //\n        return;\n      }\n\n      let data = bufferUtil.concat(\n        this._deflate[kBuffers],\n        this._deflate[kTotalLength]\n      );\n\n      if (fin) {\n        data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n      }\n\n      //\n      // Ensure that the callback will not be called again in\n      // `PerMessageDeflate#cleanup()`.\n      //\n      this._deflate[kCallback] = null;\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n        this._deflate.reset();\n      }\n\n      callback(null, data);\n    });\n  }\n}\n\nmodule.exports = PerMessageDeflate;\n\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction deflateOnData(chunk) {\n  this[kBuffers].push(chunk);\n  this[kTotalLength] += chunk.length;\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction inflateOnData(chunk) {\n  this[kTotalLength] += chunk.length;\n\n  if (\n    this[kPerMessageDeflate]._maxPayload < 1 ||\n    this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload\n  ) {\n    this[kBuffers].push(chunk);\n    return;\n  }\n\n  this[kError] = new RangeError('Max payload size exceeded');\n  this[kError].code = 'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH';\n  this[kError][kStatusCode] = 1009;\n  this.removeListener('data', inflateOnData);\n  this.reset();\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */\nfunction inflateOnError(err) {\n  //\n  // There is no need to call `Zlib#close()` as the handle is automatically\n  // closed when an error is emitted.\n  //\n  this[kPerMessageDeflate]._inflate = null;\n  err[kStatusCode] = 1007;\n  this[kCallback](err);\n}\n"], "names": [], "mappings": "AAEA,MAAM;AAEN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,WAAW,EAAE;AAErB,MAAM,aAAa,MAAM,CAAC,OAAO,OAAO,CAAC;AACzC,MAAM,UAAU,OAAO,IAAI,CAAC;IAAC;IAAM;IAAM;IAAM;CAAK;AACpD,MAAM,qBAAqB,OAAO;AAClC,MAAM,eAAe,OAAO;AAC5B,MAAM,YAAY,OAAO;AACzB,MAAM,WAAW,OAAO;AACxB,MAAM,SAAS,OAAO;AAEtB,EAAE;AACF,wEAAwE;AACxE,qFAAqF;AACrF,mDAAmD;AACnD,EAAE;AACF,qEAAqE;AACrE,EAAE;AACF,IAAI;AAEJ;;CAEC,GACD,MAAM;IACJ;;;;;;;;;;;;;;;;;;;;;;;GAuBC,GACD,YAAY,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAE;QACzC,IAAI,CAAC,WAAW,GAAG,aAAa;QAChC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAC5B,IAAI,CAAC,UAAU,GACb,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,YAAY,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG;QACpE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG;QAEhB,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,CAAC,aAAa;YAChB,MAAM,cACJ,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,YAC/B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAC9B;YACN,cAAc,IAAI,QAAQ;QAC5B;IACF;IAEA;;GAEC,GACD,WAAW,gBAAgB;QACzB,OAAO;IACT;IAEA;;;;;GAKC,GACD,QAAQ;QACN,MAAM,SAAS,CAAC;QAEhB,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YACzC,OAAO,0BAA0B,GAAG;QACtC;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YACzC,OAAO,0BAA0B,GAAG;QACtC;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;YACrC,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QACnE;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;YACrC,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QACnE,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,IAAI,MAAM;YACpD,OAAO,sBAAsB,GAAG;QAClC;QAEA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,OAAO,cAAc,EAAE;QACrB,iBAAiB,IAAI,CAAC,eAAe,CAAC;QAEtC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,GACxB,IAAI,CAAC,cAAc,CAAC,kBACpB,IAAI,CAAC,cAAc,CAAC;QAExB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA;;;;GAIC,GACD,UAAU;QACR,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,KAAK;YACnB,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,UAAU;YAEzC,IAAI,CAAC,QAAQ,CAAC,KAAK;YACnB,IAAI,CAAC,QAAQ,GAAG;YAEhB,IAAI,UAAU;gBACZ,SACE,IAAI,MACF;YAGN;QACF;IACF;IAEA;;;;;;GAMC,GACD,eAAe,MAAM,EAAE;QACrB,MAAM,OAAO,IAAI,CAAC,QAAQ;QAC1B,MAAM,WAAW,OAAO,IAAI,CAAC,CAAC;YAC5B,IACE,AAAC,KAAK,uBAAuB,KAAK,SAChC,OAAO,0BAA0B,IAClC,OAAO,sBAAsB,IAC5B,CAAC,KAAK,mBAAmB,KAAK,SAC3B,OAAO,KAAK,mBAAmB,KAAK,YACnC,KAAK,mBAAmB,GAAG,OAAO,sBAAsB,AAAC,KAC9D,OAAO,KAAK,mBAAmB,KAAK,YACnC,CAAC,OAAO,sBAAsB,EAChC;gBACA,OAAO;YACT;YAEA,OAAO;QACT;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,uBAAuB,EAAE;YAChC,SAAS,0BAA0B,GAAG;QACxC;QACA,IAAI,KAAK,uBAAuB,EAAE;YAChC,SAAS,0BAA0B,GAAG;QACxC;QACA,IAAI,OAAO,KAAK,mBAAmB,KAAK,UAAU;YAChD,SAAS,sBAAsB,GAAG,KAAK,mBAAmB;QAC5D;QACA,IAAI,OAAO,KAAK,mBAAmB,KAAK,UAAU;YAChD,SAAS,sBAAsB,GAAG,KAAK,mBAAmB;QAC5D,OAAO,IACL,SAAS,sBAAsB,KAAK,QACpC,KAAK,mBAAmB,KAAK,OAC7B;YACA,OAAO,SAAS,sBAAsB;QACxC;QAEA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,eAAe,QAAQ,EAAE;QACvB,MAAM,SAAS,QAAQ,CAAC,EAAE;QAE1B,IACE,IAAI,CAAC,QAAQ,CAAC,uBAAuB,KAAK,SAC1C,OAAO,0BAA0B,EACjC;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,OAAO,sBAAsB,EAAE;YAClC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,UAAU;gBACzD,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB;YACnE;QACF,OAAO,IACL,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,SACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,YAC5C,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EACnE;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,gBAAgB,cAAc,EAAE;QAC9B,eAAe,OAAO,CAAC,CAAC;YACtB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC;gBAC3B,IAAI,QAAQ,MAAM,CAAC,IAAI;gBAEvB,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,+BAA+B,CAAC;gBACpE;gBAEA,QAAQ,KAAK,CAAC,EAAE;gBAEhB,IAAI,QAAQ,0BAA0B;oBACpC,IAAI,UAAU,MAAM;wBAClB,MAAM,MAAM,CAAC;wBACb,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,KAAK,MAAM,IAAI;4BACjD,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;wBAEpD;wBACA,QAAQ;oBACV,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1B,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;oBAEpD;gBACF,OAAO,IAAI,QAAQ,0BAA0B;oBAC3C,MAAM,MAAM,CAAC;oBACb,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,KAAK,MAAM,IAAI;wBACjD,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;oBAEpD;oBACA,QAAQ;gBACV,OAAO,IACL,QAAQ,gCACR,QAAQ,8BACR;oBACA,IAAI,UAAU,MAAM;wBAClB,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;oBAEpD;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;gBAC9C;gBAEA,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;QAEA,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,WAAW,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC9B,YAAY,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,KAAK;gBAChC;gBACA,SAAS,KAAK;YAChB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,SAAS,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC5B,YAAY,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,KAAK;gBAC9B;gBACA,SAAS,KAAK;YAChB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,YAAY,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC/B,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,WAAW;QAE7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,MAAM,GAAG,SAAS,gBAAgB,CAAC;YACzC,MAAM,aACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WACxB,KAAK,oBAAoB,GACzB,IAAI,CAAC,MAAM,CAAC,IAAI;YAEtB,IAAI,CAAC,QAAQ,GAAG,KAAK,gBAAgB,CAAC;gBACpC,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB;gBACnC;YACF;YACA,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,IAAI;YACxC,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;YAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS;YAC1B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ;QAC3B;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE3B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACpB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAE7B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAClB,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO;YAEjC,IAAI,KAAK;gBACP,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACnB,IAAI,CAAC,QAAQ,GAAG;gBAChB,SAAS;gBACT;YACF;YAEA,MAAM,OAAO,WAAW,MAAM,CAC5B,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,IAAI,CAAC,QAAQ,CAAC,aAAa;YAG7B,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,EAAE;gBAC3C,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACnB,IAAI,CAAC,QAAQ,GAAG;YAClB,OAAO;gBACL,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;gBAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;gBAE5B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC,EAAE;oBACzD,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACrB;YACF;YAEA,SAAS,MAAM;QACjB;IACF;IAEA;;;;;;;GAOC,GACD,UAAU,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC7B,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,WAAW;QAE7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,MAAM,GAAG,SAAS,gBAAgB,CAAC;YACzC,MAAM,aACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WACxB,KAAK,oBAAoB,GACzB,IAAI,CAAC,MAAM,CAAC,IAAI;YAEtB,IAAI,CAAC,QAAQ,GAAG,KAAK,gBAAgB,CAAC;gBACpC,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB;gBACnC;YACF;YAEA,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;YAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;YAE5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ;QAC3B;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE3B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,YAAY,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,EAAE;gBACF,gEAAgE;gBAChE,EAAE;gBACF;YACF;YAEA,IAAI,OAAO,WAAW,MAAM,CAC1B,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,IAAI,CAAC,QAAQ,CAAC,aAAa;YAG7B,IAAI,KAAK;gBACP,OAAO,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,MAAM,GAAG;YACpE;YAEA,EAAE;YACF,uDAAuD;YACvD,iCAAiC;YACjC,EAAE;YACF,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;YAE3B,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;YAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;YAE5B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC,EAAE;gBACzD,IAAI,CAAC,QAAQ,CAAC,KAAK;YACrB;YAEA,SAAS,MAAM;QACjB;IACF;AACF;AAEA,OAAO,OAAO,GAAG;AAEjB;;;;;CAKC,GACD,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IACpB,IAAI,CAAC,aAAa,IAAI,MAAM,MAAM;AACpC;AAEA;;;;;CAKC,GACD,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,aAAa,IAAI,MAAM,MAAM;IAElC,IACE,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,KACvC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAC1D;QACA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACpB;IACF;IAEA,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW;IAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;IACpB,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG;IAC5B,IAAI,CAAC,cAAc,CAAC,QAAQ;IAC5B,IAAI,CAAC,KAAK;AACZ;AAEA;;;;;CAKC,GACD,SAAS,eAAe,GAAG;IACzB,EAAE;IACF,yEAAyE;IACzE,mCAAmC;IACnC,EAAE;IACF,IAAI,CAAC,mBAAmB,CAAC,QAAQ,GAAG;IACpC,GAAG,CAAC,YAAY,GAAG;IACnB,IAAI,CAAC,UAAU,CAAC;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3340, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/validation.js"], "sourcesContent": ["'use strict';\n\nconst { isUtf8 } = require('buffer');\n\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 0 - 15\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 16 - 31\n  0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, // 32 - 47\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, // 48 - 63\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 64 - 79\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, // 80 - 95\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 96 - 111\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0 // 112 - 127\n];\n\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */\nfunction isValidStatusCode(code) {\n  return (\n    (code >= 1000 &&\n      code <= 1014 &&\n      code !== 1004 &&\n      code !== 1005 &&\n      code !== 1006) ||\n    (code >= 3000 && code <= 4999)\n  );\n}\n\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */\nfunction _isValidUTF8(buf) {\n  const len = buf.length;\n  let i = 0;\n\n  while (i < len) {\n    if ((buf[i] & 0x80) === 0) {\n      // 0xxxxxxx\n      i++;\n    } else if ((buf[i] & 0xe0) === 0xc0) {\n      // 110xxxxx 10xxxxxx\n      if (\n        i + 1 === len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i] & 0xfe) === 0xc0 // Overlong\n      ) {\n        return false;\n      }\n\n      i += 2;\n    } else if ((buf[i] & 0xf0) === 0xe0) {\n      // 1110xxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 2 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80) || // Overlong\n        (buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0) // Surrogate (U+D800 - U+DFFF)\n      ) {\n        return false;\n      }\n\n      i += 3;\n    } else if ((buf[i] & 0xf8) === 0xf0) {\n      // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 3 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i + 3] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80) || // Overlong\n        (buf[i] === 0xf4 && buf[i + 1] > 0x8f) ||\n        buf[i] > 0xf4 // > U+10FFFF\n      ) {\n        return false;\n      }\n\n      i += 4;\n    } else {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nmodule.exports = {\n  isValidStatusCode,\n  isValidUTF8: _isValidUTF8,\n  tokenChars\n};\n\nif (isUtf8) {\n  module.exports.isValidUTF8 = function (buf) {\n    return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n  };\n} /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n  try {\n    const isValidUTF8 = require('utf-8-validate');\n\n    module.exports.isValidUTF8 = function (buf) {\n      return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,MAAM,EAAE;AAEhB,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,+CAA+C;AAC/C,8CAA8C;AAC9C,EAAE;AACF,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,MAAM;AACN,EAAE;AACF,kBAAkB;AAClB,MAAM,aAAa;IACjB;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,EAAE,YAAY;CAC5D;AAED;;;;;;CAMC,GACD,SAAS,kBAAkB,IAAI;IAC7B,OACE,AAAC,QAAQ,QACP,QAAQ,QACR,SAAS,QACT,SAAS,QACT,SAAS,QACV,QAAQ,QAAQ,QAAQ;AAE7B;AAEA;;;;;;;;CAQC,GACD,SAAS,aAAa,GAAG;IACvB,MAAM,MAAM,IAAI,MAAM;IACtB,IAAI,IAAI;IAER,MAAO,IAAI,IAAK;QACd,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,GAAG;YACzB,WAAW;YACX;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YACnC,oBAAoB;YACpB,IACE,IAAI,MAAM,OACV,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,KAAK,WAAW;cACpC;gBACA,OAAO;YACT;YAEA,KAAK;QACP,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YACnC,6BAA6B;YAC7B,IACE,IAAI,KAAK,OACT,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACvB,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QAC3C,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,KAAM,8BAA8B;cAChF;gBACA,OAAO;YACT;YAEA,KAAK;QACP,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YACnC,sCAAsC;YACtC,IACE,IAAI,KAAK,OACT,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACvB,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QAC3C,GAAG,CAAC,EAAE,KAAK,QAAQ,GAAG,CAAC,IAAI,EAAE,GAAG,QACjC,GAAG,CAAC,EAAE,GAAG,KAAK,aAAa;cAC3B;gBACA,OAAO;YACT;YAEA,KAAK;QACP,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG;IACf;IACA,aAAa;IACb;AACF;AAEA,IAAI,QAAQ;IACV,OAAO,OAAO,CAAC,WAAW,GAAG,SAAU,GAAG;QACxC,OAAO,IAAI,MAAM,GAAG,KAAK,aAAa,OAAO,OAAO;IACtD;AACF,OAAmC,IAAI,CAAC,QAAQ,GAAG,CAAC,oBAAoB,EAAE;IACxE,IAAI;QACF,MAAM;;;;;QAEN,OAAO,OAAO,CAAC,WAAW,GAAG,SAAU,GAAG;YACxC,OAAO,IAAI,MAAM,GAAG,KAAK,aAAa,OAAO,YAAY;QAC3D;IACF,EAAE,OAAO,GAAG;IACV,oCAAoC;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3563, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/receiver.js"], "sourcesContent": ["'use strict';\n\nconst { Writable } = require('stream');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  kStatusCode,\n  kWebSocket\n} = require('./constants');\nconst { concat, toArrayBuffer, unmask } = require('./buffer-util');\nconst { isValidStatusCode, isValidUTF8 } = require('./validation');\n\nconst FastBuffer = Buffer[Symbol.species];\n\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */\nclass Receiver extends Writable {\n  /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */\n  constructor(options = {}) {\n    super();\n\n    this._allowSynchronousEvents =\n      options.allowSynchronousEvents !== undefined\n        ? options.allowSynchronousEvents\n        : true;\n    this._binaryType = options.binaryType || BINARY_TYPES[0];\n    this._extensions = options.extensions || {};\n    this._isServer = !!options.isServer;\n    this._maxPayload = options.maxPayload | 0;\n    this._skipUTF8Validation = !!options.skipUTF8Validation;\n    this[kWebSocket] = undefined;\n\n    this._bufferedBytes = 0;\n    this._buffers = [];\n\n    this._compressed = false;\n    this._payloadLength = 0;\n    this._mask = undefined;\n    this._fragmented = 0;\n    this._masked = false;\n    this._fin = false;\n    this._opcode = 0;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragments = [];\n\n    this._errored = false;\n    this._loop = false;\n    this._state = GET_INFO;\n  }\n\n  /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */\n  _write(chunk, encoding, cb) {\n    if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n\n    this._bufferedBytes += chunk.length;\n    this._buffers.push(chunk);\n    this.startLoop(cb);\n  }\n\n  /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */\n  consume(n) {\n    this._bufferedBytes -= n;\n\n    if (n === this._buffers[0].length) return this._buffers.shift();\n\n    if (n < this._buffers[0].length) {\n      const buf = this._buffers[0];\n      this._buffers[0] = new FastBuffer(\n        buf.buffer,\n        buf.byteOffset + n,\n        buf.length - n\n      );\n\n      return new FastBuffer(buf.buffer, buf.byteOffset, n);\n    }\n\n    const dst = Buffer.allocUnsafe(n);\n\n    do {\n      const buf = this._buffers[0];\n      const offset = dst.length - n;\n\n      if (n >= buf.length) {\n        dst.set(this._buffers.shift(), offset);\n      } else {\n        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n        this._buffers[0] = new FastBuffer(\n          buf.buffer,\n          buf.byteOffset + n,\n          buf.length - n\n        );\n      }\n\n      n -= buf.length;\n    } while (n > 0);\n\n    return dst;\n  }\n\n  /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  startLoop(cb) {\n    this._loop = true;\n\n    do {\n      switch (this._state) {\n        case GET_INFO:\n          this.getInfo(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_16:\n          this.getPayloadLength16(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_64:\n          this.getPayloadLength64(cb);\n          break;\n        case GET_MASK:\n          this.getMask();\n          break;\n        case GET_DATA:\n          this.getData(cb);\n          break;\n        case INFLATING:\n        case DEFER_EVENT:\n          this._loop = false;\n          return;\n      }\n    } while (this._loop);\n\n    if (!this._errored) cb();\n  }\n\n  /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getInfo(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(2);\n\n    if ((buf[0] & 0x30) !== 0x00) {\n      const error = this.createError(\n        RangeError,\n        'RSV2 and RSV3 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_2_3'\n      );\n\n      cb(error);\n      return;\n    }\n\n    const compressed = (buf[0] & 0x40) === 0x40;\n\n    if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n      const error = this.createError(\n        RangeError,\n        'RSV1 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_1'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._fin = (buf[0] & 0x80) === 0x80;\n    this._opcode = buf[0] & 0x0f;\n    this._payloadLength = buf[1] & 0x7f;\n\n    if (this._opcode === 0x00) {\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (!this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          'invalid opcode 0',\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._opcode = this._fragmented;\n    } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n      if (this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          `invalid opcode ${this._opcode}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._compressed = compressed;\n    } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n      if (!this._fin) {\n        const error = this.createError(\n          RangeError,\n          'FIN must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_FIN'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (\n        this._payloadLength > 0x7d ||\n        (this._opcode === 0x08 && this._payloadLength === 1)\n      ) {\n        const error = this.createError(\n          RangeError,\n          `invalid payload length ${this._payloadLength}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    } else {\n      const error = this.createError(\n        RangeError,\n        `invalid opcode ${this._opcode}`,\n        true,\n        1002,\n        'WS_ERR_INVALID_OPCODE'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n    this._masked = (buf[1] & 0x80) === 0x80;\n\n    if (this._isServer) {\n      if (!this._masked) {\n        const error = this.createError(\n          RangeError,\n          'MASK must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_MASK'\n        );\n\n        cb(error);\n        return;\n      }\n    } else if (this._masked) {\n      const error = this.createError(\n        RangeError,\n        'MASK must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_MASK'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;\n    else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;\n    else this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength16(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    this._payloadLength = this.consume(2).readUInt16BE(0);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength64(cb) {\n    if (this._bufferedBytes < 8) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(8);\n    const num = buf.readUInt32BE(0);\n\n    //\n    // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n    // if payload length is greater than this number.\n    //\n    if (num > Math.pow(2, 53 - 32) - 1) {\n      const error = this.createError(\n        RangeError,\n        'Unsupported WebSocket frame: payload length > 2^53 - 1',\n        false,\n        1009,\n        'WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  haveLength(cb) {\n    if (this._payloadLength && this._opcode < 0x08) {\n      this._totalPayloadLength += this._payloadLength;\n      if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n        const error = this.createError(\n          RangeError,\n          'Max payload size exceeded',\n          false,\n          1009,\n          'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    }\n\n    if (this._masked) this._state = GET_MASK;\n    else this._state = GET_DATA;\n  }\n\n  /**\n   * Reads mask bytes.\n   *\n   * @private\n   */\n  getMask() {\n    if (this._bufferedBytes < 4) {\n      this._loop = false;\n      return;\n    }\n\n    this._mask = this.consume(4);\n    this._state = GET_DATA;\n  }\n\n  /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getData(cb) {\n    let data = EMPTY_BUFFER;\n\n    if (this._payloadLength) {\n      if (this._bufferedBytes < this._payloadLength) {\n        this._loop = false;\n        return;\n      }\n\n      data = this.consume(this._payloadLength);\n\n      if (\n        this._masked &&\n        (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0\n      ) {\n        unmask(data, this._mask);\n      }\n    }\n\n    if (this._opcode > 0x07) {\n      this.controlMessage(data, cb);\n      return;\n    }\n\n    if (this._compressed) {\n      this._state = INFLATING;\n      this.decompress(data, cb);\n      return;\n    }\n\n    if (data.length) {\n      //\n      // This message is not compressed so its length is the sum of the payload\n      // length of all fragments.\n      //\n      this._messageLength = this._totalPayloadLength;\n      this._fragments.push(data);\n    }\n\n    this.dataMessage(cb);\n  }\n\n  /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */\n  decompress(data, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n\n    perMessageDeflate.decompress(data, this._fin, (err, buf) => {\n      if (err) return cb(err);\n\n      if (buf.length) {\n        this._messageLength += buf.length;\n        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n          const error = this.createError(\n            RangeError,\n            'Max payload size exceeded',\n            false,\n            1009,\n            'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._fragments.push(buf);\n      }\n\n      this.dataMessage(cb);\n      if (this._state === GET_INFO) this.startLoop(cb);\n    });\n  }\n\n  /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  dataMessage(cb) {\n    if (!this._fin) {\n      this._state = GET_INFO;\n      return;\n    }\n\n    const messageLength = this._messageLength;\n    const fragments = this._fragments;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragmented = 0;\n    this._fragments = [];\n\n    if (this._opcode === 2) {\n      let data;\n\n      if (this._binaryType === 'nodebuffer') {\n        data = concat(fragments, messageLength);\n      } else if (this._binaryType === 'arraybuffer') {\n        data = toArrayBuffer(concat(fragments, messageLength));\n      } else {\n        data = fragments;\n      }\n\n      if (this._allowSynchronousEvents) {\n        this.emit('message', data, true);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', data, true);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    } else {\n      const buf = concat(fragments, messageLength);\n\n      if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n        const error = this.createError(\n          Error,\n          'invalid UTF-8 sequence',\n          true,\n          1007,\n          'WS_ERR_INVALID_UTF8'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (this._state === INFLATING || this._allowSynchronousEvents) {\n        this.emit('message', buf, false);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', buf, false);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    }\n  }\n\n  /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  controlMessage(data, cb) {\n    if (this._opcode === 0x08) {\n      if (data.length === 0) {\n        this._loop = false;\n        this.emit('conclude', 1005, EMPTY_BUFFER);\n        this.end();\n      } else {\n        const code = data.readUInt16BE(0);\n\n        if (!isValidStatusCode(code)) {\n          const error = this.createError(\n            RangeError,\n            `invalid status code ${code}`,\n            true,\n            1002,\n            'WS_ERR_INVALID_CLOSE_CODE'\n          );\n\n          cb(error);\n          return;\n        }\n\n        const buf = new FastBuffer(\n          data.buffer,\n          data.byteOffset + 2,\n          data.length - 2\n        );\n\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          const error = this.createError(\n            Error,\n            'invalid UTF-8 sequence',\n            true,\n            1007,\n            'WS_ERR_INVALID_UTF8'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._loop = false;\n        this.emit('conclude', code, buf);\n        this.end();\n      }\n\n      this._state = GET_INFO;\n      return;\n    }\n\n    if (this._allowSynchronousEvents) {\n      this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n      this._state = GET_INFO;\n    } else {\n      this._state = DEFER_EVENT;\n      setImmediate(() => {\n        this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n        this._state = GET_INFO;\n        this.startLoop(cb);\n      });\n    }\n  }\n\n  /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */\n  createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n    this._loop = false;\n    this._errored = true;\n\n    const err = new ErrorCtor(\n      prefix ? `Invalid WebSocket frame: ${message}` : message\n    );\n\n    Error.captureStackTrace(err, this.createError);\n    err.code = errorCode;\n    err[kStatusCode] = statusCode;\n    return err;\n  }\n}\n\nmodule.exports = Receiver;\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AACN,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,UAAU,EACX;AACD,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE;AACvC,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE;AAExC,MAAM,aAAa,MAAM,CAAC,OAAO,OAAO,CAAC;AAEzC,MAAM,WAAW;AACjB,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB;AAC9B,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,cAAc;AAEpB;;;;CAIC,GACD,MAAM,iBAAiB;IACrB;;;;;;;;;;;;;;;GAeC,GACD,YAAY,UAAU,CAAC,CAAC,CAAE;QACxB,KAAK;QAEL,IAAI,CAAC,uBAAuB,GAC1B,QAAQ,sBAAsB,KAAK,YAC/B,QAAQ,sBAAsB,GAC9B;QACN,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU,IAAI,YAAY,CAAC,EAAE;QACxD,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU,IAAI,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,QAAQ;QACnC,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU,GAAG;QACxC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,QAAQ,kBAAkB;QACvD,IAAI,CAAC,WAAW,GAAG;QAEnB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAElB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE;QAEpB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;;;;;;GAOC,GACD,OAAO,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;QAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,MAAM,IAAI,UAAU,OAAO;QAE7D,IAAI,CAAC,cAAc,IAAI,MAAM,MAAM;QACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC;IACjB;IAEA;;;;;;GAMC,GACD,QAAQ,CAAC,EAAE;QACT,IAAI,CAAC,cAAc,IAAI;QAEvB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK;QAE7D,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE;YAC/B,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,WACrB,IAAI,MAAM,EACV,IAAI,UAAU,GAAG,GACjB,IAAI,MAAM,GAAG;YAGf,OAAO,IAAI,WAAW,IAAI,MAAM,EAAE,IAAI,UAAU,EAAE;QACpD;QAEA,MAAM,MAAM,OAAO,WAAW,CAAC;QAE/B,GAAG;YACD,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,MAAM,SAAS,IAAI,MAAM,GAAG;YAE5B,IAAI,KAAK,IAAI,MAAM,EAAE;gBACnB,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;YACjC,OAAO;gBACL,IAAI,GAAG,CAAC,IAAI,WAAW,IAAI,MAAM,EAAE,IAAI,UAAU,EAAE,IAAI;gBACvD,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,WACrB,IAAI,MAAM,EACV,IAAI,UAAU,GAAG,GACjB,IAAI,MAAM,GAAG;YAEjB;YAEA,KAAK,IAAI,MAAM;QACjB,QAAS,IAAI,EAAG;QAEhB,OAAO;IACT;IAEA;;;;;GAKC,GACD,UAAU,EAAE,EAAE;QACZ,IAAI,CAAC,KAAK,GAAG;QAEb,GAAG;YACD,OAAQ,IAAI,CAAC,MAAM;gBACjB,KAAK;oBACH,IAAI,CAAC,OAAO,CAAC;oBACb;gBACF,KAAK;oBACH,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACF,KAAK;oBACH,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACF,KAAK;oBACH,IAAI,CAAC,OAAO;oBACZ;gBACF,KAAK;oBACH,IAAI,CAAC,OAAO,CAAC;oBACb;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,KAAK,GAAG;oBACb;YACJ;QACF,QAAS,IAAI,CAAC,KAAK,CAAE;QAErB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IACtB;IAEA;;;;;GAKC,GACD,QAAQ,EAAE,EAAE;QACV,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;QAEzB,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YAC5B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,+BACA,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,MAAM,aAAa,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;QAEvC,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,EAAE;YACpE,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;QAChC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,EAAE,GAAG;QAE/B,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACzB,IAAI,YAAY;gBACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,oBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;QACjC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,KAAK,MAAM;YACzD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,EAChC,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,CAAC,WAAW,GAAG;QACrB,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM;YACrD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,mBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,YAAY;gBACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IACE,IAAI,CAAC,cAAc,GAAG,QACrB,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,cAAc,KAAK,GAClD;gBACA,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,uBAAuB,EAAE,IAAI,CAAC,cAAc,EAAE,EAC/C,MACA,MACA;gBAGF,GAAG;gBACH;YACF;QACF,OAAO;YACL,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,EAChC,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO;QACpE,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;QAEnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,oBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;QACF,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE;YACvB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,IAAI,CAAC,MAAM,GAAG;aAC1C,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,IAAI,CAAC,MAAM,GAAG;aAC/C,IAAI,CAAC,UAAU,CAAC;IACvB;IAEA;;;;;GAKC,GACD,mBAAmB,EAAE,EAAE;QACrB,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC;QACnD,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA;;;;;GAKC,GACD,mBAAmB,EAAE,EAAE;QACrB,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;QACzB,MAAM,MAAM,IAAI,YAAY,CAAC;QAE7B,EAAE;QACF,2EAA2E;QAC3E,iDAAiD;QACjD,EAAE;QACF,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,GAAG;YAClC,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,0DACA,OACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,CAAC,cAAc,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,MAAM,IAAI,YAAY,CAAC;QAC/D,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA;;;;;GAKC,GACD,WAAW,EAAE,EAAE;QACb,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM;YAC9C,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,cAAc;YAC/C,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG;gBACvE,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,6BACA,OACA,MACA;gBAGF,GAAG;gBACH;YACF;QACF;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,GAAG;aAC3B,IAAI,CAAC,MAAM,GAAG;IACrB;IAEA;;;;GAIC,GACD,UAAU;QACR,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;;;;GAKC,GACD,QAAQ,EAAE,EAAE;QACV,IAAI,OAAO;QAEX,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE;gBAC7C,IAAI,CAAC,KAAK,GAAG;gBACb;YACF;YAEA,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc;YAEvC,IACE,IAAI,CAAC,OAAO,IACZ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,GACpE;gBACA,OAAO,MAAM,IAAI,CAAC,KAAK;YACzB;QACF;QAEA,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM;YACvB,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B;QACF;QAEA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,UAAU,CAAC,MAAM;YACtB;QACF;QAEA,IAAI,KAAK,MAAM,EAAE;YACf,EAAE;YACF,yEAAyE;YACzE,2BAA2B;YAC3B,EAAE;YACF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB;YAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACvB;QAEA,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA;;;;;;GAMC,GACD,WAAW,IAAI,EAAE,EAAE,EAAE;QACnB,MAAM,oBAAoB,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC;QAE3E,kBAAkB,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK;YAClD,IAAI,KAAK,OAAO,GAAG;YAEnB,IAAI,IAAI,MAAM,EAAE;gBACd,IAAI,CAAC,cAAc,IAAI,IAAI,MAAM;gBACjC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG;oBAClE,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,6BACA,OACA,MACA;oBAGF,GAAG;oBACH;gBACF;gBAEA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACvB;YAEA,IAAI,CAAC,WAAW,CAAC;YACjB,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,SAAS,CAAC;QAC/C;IACF;IAEA;;;;;GAKC,GACD,YAAY,EAAE,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,MAAM,GAAG;YACd;QACF;QAEA,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,YAAY,IAAI,CAAC,UAAU;QAEjC,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,EAAE;QAEpB,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG;YACtB,IAAI;YAEJ,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc;gBACrC,OAAO,OAAO,WAAW;YAC3B,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,eAAe;gBAC7C,OAAO,cAAc,OAAO,WAAW;YACzC,OAAO;gBACL,OAAO;YACT;YAEA,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAChC,IAAI,CAAC,IAAI,CAAC,WAAW,MAAM;gBAC3B,IAAI,CAAC,MAAM,GAAG;YAChB,OAAO;gBACL,IAAI,CAAC,MAAM,GAAG;gBACd,aAAa;oBACX,IAAI,CAAC,IAAI,CAAC,WAAW,MAAM;oBAC3B,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,CAAC,SAAS,CAAC;gBACjB;YACF;QACF,OAAO;YACL,MAAM,MAAM,OAAO,WAAW;YAE9B,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,YAAY,MAAM;gBAClD,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,OACA,0BACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,uBAAuB,EAAE;gBAC7D,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK;gBAC1B,IAAI,CAAC,MAAM,GAAG;YAChB,OAAO;gBACL,IAAI,CAAC,MAAM,GAAG;gBACd,aAAa;oBACX,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK;oBAC1B,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,CAAC,SAAS,CAAC;gBACjB;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACD,eAAe,IAAI,EAAE,EAAE,EAAE;QACvB,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM;gBAC5B,IAAI,CAAC,GAAG;YACV,OAAO;gBACL,MAAM,OAAO,KAAK,YAAY,CAAC;gBAE/B,IAAI,CAAC,kBAAkB,OAAO;oBAC5B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,oBAAoB,EAAE,MAAM,EAC7B,MACA,MACA;oBAGF,GAAG;oBACH;gBACF;gBAEA,MAAM,MAAM,IAAI,WACd,KAAK,MAAM,EACX,KAAK,UAAU,GAAG,GAClB,KAAK,MAAM,GAAG;gBAGhB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,YAAY,MAAM;oBAClD,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,OACA,0BACA,MACA,MACA;oBAGF,GAAG;oBACH;gBACF;gBAEA,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM;gBAC5B,IAAI,CAAC,GAAG;YACV;YAEA,IAAI,CAAC,MAAM,GAAG;YACd;QACF;QAEA,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,SAAS,QAAQ;YACnD,IAAI,CAAC,MAAM,GAAG;QAChB,OAAO;YACL,IAAI,CAAC,MAAM,GAAG;YACd,aAAa;gBACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,SAAS,QAAQ;gBACnD,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,SAAS,CAAC;YACjB;QACF;IACF;IAEA;;;;;;;;;;;GAWC,GACD,YAAY,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE;QAC7D,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAEhB,MAAM,MAAM,IAAI,UACd,SAAS,CAAC,yBAAyB,EAAE,SAAS,GAAG;QAGnD,MAAM,iBAAiB,CAAC,KAAK,IAAI,CAAC,WAAW;QAC7C,IAAI,IAAI,GAAG;QACX,GAAG,CAAC,YAAY,GAAG;QACnB,OAAO;IACT;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4029, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/sender.js"], "sourcesContent": ["/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */\n\n'use strict';\n\nconst { Duplex } = require('stream');\nconst { randomFillSync } = require('crypto');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst { EMPTY_BUFFER } = require('./constants');\nconst { isValidStatusCode } = require('./validation');\nconst { mask: applyMask, toBuffer } = require('./buffer-util');\n\nconst kByteLength = Symbol('kByteLength');\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\n\n/**\n * HyBi Sender implementation.\n */\nclass Sender {\n  /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */\n  constructor(socket, extensions, generateMask) {\n    this._extensions = extensions || {};\n\n    if (generateMask) {\n      this._generateMask = generateMask;\n      this._maskBuffer = Buffer.alloc(4);\n    }\n\n    this._socket = socket;\n\n    this._firstFragment = true;\n    this._compress = false;\n\n    this._bufferedBytes = 0;\n    this._deflating = false;\n    this._queue = [];\n  }\n\n  /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */\n  static frame(data, options) {\n    let mask;\n    let merge = false;\n    let offset = 2;\n    let skipMasking = false;\n\n    if (options.mask) {\n      mask = options.maskBuffer || maskBuffer;\n\n      if (options.generateMask) {\n        options.generateMask(mask);\n      } else {\n        if (randomPoolPointer === RANDOM_POOL_SIZE) {\n          /* istanbul ignore else  */\n          if (randomPool === undefined) {\n            //\n            // This is lazily initialized because server-sent frames must not\n            // be masked so it may never be used.\n            //\n            randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n          }\n\n          randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n          randomPoolPointer = 0;\n        }\n\n        mask[0] = randomPool[randomPoolPointer++];\n        mask[1] = randomPool[randomPoolPointer++];\n        mask[2] = randomPool[randomPoolPointer++];\n        mask[3] = randomPool[randomPoolPointer++];\n      }\n\n      skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n      offset = 6;\n    }\n\n    let dataLength;\n\n    if (typeof data === 'string') {\n      if (\n        (!options.mask || skipMasking) &&\n        options[kByteLength] !== undefined\n      ) {\n        dataLength = options[kByteLength];\n      } else {\n        data = Buffer.from(data);\n        dataLength = data.length;\n      }\n    } else {\n      dataLength = data.length;\n      merge = options.mask && options.readOnly && !skipMasking;\n    }\n\n    let payloadLength = dataLength;\n\n    if (dataLength >= 65536) {\n      offset += 8;\n      payloadLength = 127;\n    } else if (dataLength > 125) {\n      offset += 2;\n      payloadLength = 126;\n    }\n\n    const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n\n    target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n    if (options.rsv1) target[0] |= 0x40;\n\n    target[1] = payloadLength;\n\n    if (payloadLength === 126) {\n      target.writeUInt16BE(dataLength, 2);\n    } else if (payloadLength === 127) {\n      target[2] = target[3] = 0;\n      target.writeUIntBE(dataLength, 4, 6);\n    }\n\n    if (!options.mask) return [target, data];\n\n    target[1] |= 0x80;\n    target[offset - 4] = mask[0];\n    target[offset - 3] = mask[1];\n    target[offset - 2] = mask[2];\n    target[offset - 1] = mask[3];\n\n    if (skipMasking) return [target, data];\n\n    if (merge) {\n      applyMask(data, mask, target, offset, dataLength);\n      return [target];\n    }\n\n    applyMask(data, mask, data, 0, dataLength);\n    return [target, data];\n  }\n\n  /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  close(code, data, mask, cb) {\n    let buf;\n\n    if (code === undefined) {\n      buf = EMPTY_BUFFER;\n    } else if (typeof code !== 'number' || !isValidStatusCode(code)) {\n      throw new TypeError('First argument must be a valid error code number');\n    } else if (data === undefined || !data.length) {\n      buf = Buffer.allocUnsafe(2);\n      buf.writeUInt16BE(code, 0);\n    } else {\n      const length = Buffer.byteLength(data);\n\n      if (length > 123) {\n        throw new RangeError('The message must not be greater than 123 bytes');\n      }\n\n      buf = Buffer.allocUnsafe(2 + length);\n      buf.writeUInt16BE(code, 0);\n\n      if (typeof data === 'string') {\n        buf.write(data, 2);\n      } else {\n        buf.set(data, 2);\n      }\n    }\n\n    const options = {\n      [kByteLength]: buf.length,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x08,\n      readOnly: false,\n      rsv1: false\n    };\n\n    if (this._deflating) {\n      this.enqueue([this.dispatch, buf, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(buf, options), cb);\n    }\n  }\n\n  /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  ping(data, mask, cb) {\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (byteLength > 125) {\n      throw new RangeError('The data size must not be greater than 125 bytes');\n    }\n\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x09,\n      readOnly,\n      rsv1: false\n    };\n\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n\n  /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  pong(data, mask, cb) {\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (byteLength > 125) {\n      throw new RangeError('The data size must not be greater than 125 bytes');\n    }\n\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x0a,\n      readOnly,\n      rsv1: false\n    };\n\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n\n  /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  send(data, options, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n    let opcode = options.binary ? 2 : 1;\n    let rsv1 = options.compress;\n\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (this._firstFragment) {\n      this._firstFragment = false;\n      if (\n        rsv1 &&\n        perMessageDeflate &&\n        perMessageDeflate.params[\n          perMessageDeflate._isServer\n            ? 'server_no_context_takeover'\n            : 'client_no_context_takeover'\n        ]\n      ) {\n        rsv1 = byteLength >= perMessageDeflate._threshold;\n      }\n      this._compress = rsv1;\n    } else {\n      rsv1 = false;\n      opcode = 0;\n    }\n\n    if (options.fin) this._firstFragment = true;\n\n    if (perMessageDeflate) {\n      const opts = {\n        [kByteLength]: byteLength,\n        fin: options.fin,\n        generateMask: this._generateMask,\n        mask: options.mask,\n        maskBuffer: this._maskBuffer,\n        opcode,\n        readOnly,\n        rsv1\n      };\n\n      if (this._deflating) {\n        this.enqueue([this.dispatch, data, this._compress, opts, cb]);\n      } else {\n        this.dispatch(data, this._compress, opts, cb);\n      }\n    } else {\n      this.sendFrame(\n        Sender.frame(data, {\n          [kByteLength]: byteLength,\n          fin: options.fin,\n          generateMask: this._generateMask,\n          mask: options.mask,\n          maskBuffer: this._maskBuffer,\n          opcode,\n          readOnly,\n          rsv1: false\n        }),\n        cb\n      );\n    }\n  }\n\n  /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  dispatch(data, compress, options, cb) {\n    if (!compress) {\n      this.sendFrame(Sender.frame(data, options), cb);\n      return;\n    }\n\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n\n    this._bufferedBytes += options[kByteLength];\n    this._deflating = true;\n    perMessageDeflate.compress(data, options.fin, (_, buf) => {\n      if (this._socket.destroyed) {\n        const err = new Error(\n          'The socket was closed while data was being compressed'\n        );\n\n        if (typeof cb === 'function') cb(err);\n\n        for (let i = 0; i < this._queue.length; i++) {\n          const params = this._queue[i];\n          const callback = params[params.length - 1];\n\n          if (typeof callback === 'function') callback(err);\n        }\n\n        return;\n      }\n\n      this._bufferedBytes -= options[kByteLength];\n      this._deflating = false;\n      options.readOnly = false;\n      this.sendFrame(Sender.frame(buf, options), cb);\n      this.dequeue();\n    });\n  }\n\n  /**\n   * Executes queued send operations.\n   *\n   * @private\n   */\n  dequeue() {\n    while (!this._deflating && this._queue.length) {\n      const params = this._queue.shift();\n\n      this._bufferedBytes -= params[3][kByteLength];\n      Reflect.apply(params[0], this, params.slice(1));\n    }\n  }\n\n  /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */\n  enqueue(params) {\n    this._bufferedBytes += params[3][kByteLength];\n    this._queue.push(params);\n  }\n\n  /**\n   * Sends a frame.\n   *\n   * @param {Buffer[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  sendFrame(list, cb) {\n    if (list.length === 2) {\n      this._socket.cork();\n      this._socket.write(list[0]);\n      this._socket.write(list[1], cb);\n      this._socket.uncork();\n    } else {\n      this._socket.write(list[0], cb);\n    }\n  }\n}\n\nmodule.exports = Sender;\n"], "names": [], "mappings": "AAAA,wEAAwE,GAIxE,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,cAAc,EAAE;AAExB,MAAM;AACN,MAAM,EAAE,YAAY,EAAE;AACtB,MAAM,EAAE,iBAAiB,EAAE;AAC3B,MAAM,EAAE,MAAM,SAAS,EAAE,QAAQ,EAAE;AAEnC,MAAM,cAAc,OAAO;AAC3B,MAAM,aAAa,OAAO,KAAK,CAAC;AAChC,MAAM,mBAAmB,IAAI;AAC7B,IAAI;AACJ,IAAI,oBAAoB;AAExB;;CAEC,GACD,MAAM;IACJ;;;;;;;GAOC,GACD,YAAY,MAAM,EAAE,UAAU,EAAE,YAAY,CAAE;QAC5C,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC;QAElC,IAAI,cAAc;YAChB,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,WAAW,GAAG,OAAO,KAAK,CAAC;QAClC;QAEA,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG,EAAE;IAClB;IAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,OAAO,MAAM,IAAI,EAAE,OAAO,EAAE;QAC1B,IAAI;QACJ,IAAI,QAAQ;QACZ,IAAI,SAAS;QACb,IAAI,cAAc;QAElB,IAAI,QAAQ,IAAI,EAAE;YAChB,OAAO,QAAQ,UAAU,IAAI;YAE7B,IAAI,QAAQ,YAAY,EAAE;gBACxB,QAAQ,YAAY,CAAC;YACvB,OAAO;gBACL,IAAI,sBAAsB,kBAAkB;oBAC1C,yBAAyB,GACzB,IAAI,eAAe,WAAW;wBAC5B,EAAE;wBACF,iEAAiE;wBACjE,qCAAqC;wBACrC,EAAE;wBACF,aAAa,OAAO,KAAK,CAAC;oBAC5B;oBAEA,eAAe,YAAY,GAAG;oBAC9B,oBAAoB;gBACtB;gBAEA,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;gBACzC,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;gBACzC,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;gBACzC,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;YAC3C;YAEA,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,MAAM;YAC1D,SAAS;QACX;QAEA,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,IACE,CAAC,CAAC,QAAQ,IAAI,IAAI,WAAW,KAC7B,OAAO,CAAC,YAAY,KAAK,WACzB;gBACA,aAAa,OAAO,CAAC,YAAY;YACnC,OAAO;gBACL,OAAO,OAAO,IAAI,CAAC;gBACnB,aAAa,KAAK,MAAM;YAC1B;QACF,OAAO;YACL,aAAa,KAAK,MAAM;YACxB,QAAQ,QAAQ,IAAI,IAAI,QAAQ,QAAQ,IAAI,CAAC;QAC/C;QAEA,IAAI,gBAAgB;QAEpB,IAAI,cAAc,OAAO;YACvB,UAAU;YACV,gBAAgB;QAClB,OAAO,IAAI,aAAa,KAAK;YAC3B,UAAU;YACV,gBAAgB;QAClB;QAEA,MAAM,SAAS,OAAO,WAAW,CAAC,QAAQ,aAAa,SAAS;QAEhE,MAAM,CAAC,EAAE,GAAG,QAAQ,GAAG,GAAG,QAAQ,MAAM,GAAG,OAAO,QAAQ,MAAM;QAChE,IAAI,QAAQ,IAAI,EAAE,MAAM,CAAC,EAAE,IAAI;QAE/B,MAAM,CAAC,EAAE,GAAG;QAEZ,IAAI,kBAAkB,KAAK;YACzB,OAAO,aAAa,CAAC,YAAY;QACnC,OAAO,IAAI,kBAAkB,KAAK;YAChC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG;YACxB,OAAO,WAAW,CAAC,YAAY,GAAG;QACpC;QAEA,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO;YAAC;YAAQ;SAAK;QAExC,MAAM,CAAC,EAAE,IAAI;QACb,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAE5B,IAAI,aAAa,OAAO;YAAC;YAAQ;SAAK;QAEtC,IAAI,OAAO;YACT,UAAU,MAAM,MAAM,QAAQ,QAAQ;YACtC,OAAO;gBAAC;aAAO;QACjB;QAEA,UAAU,MAAM,MAAM,MAAM,GAAG;QAC/B,OAAO;YAAC;YAAQ;SAAK;IACvB;IAEA;;;;;;;;GAQC,GACD,MAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QAC1B,IAAI;QAEJ,IAAI,SAAS,WAAW;YACtB,MAAM;QACR,OAAO,IAAI,OAAO,SAAS,YAAY,CAAC,kBAAkB,OAAO;YAC/D,MAAM,IAAI,UAAU;QACtB,OAAO,IAAI,SAAS,aAAa,CAAC,KAAK,MAAM,EAAE;YAC7C,MAAM,OAAO,WAAW,CAAC;YACzB,IAAI,aAAa,CAAC,MAAM;QAC1B,OAAO;YACL,MAAM,SAAS,OAAO,UAAU,CAAC;YAEjC,IAAI,SAAS,KAAK;gBAChB,MAAM,IAAI,WAAW;YACvB;YAEA,MAAM,OAAO,WAAW,CAAC,IAAI;YAC7B,IAAI,aAAa,CAAC,MAAM;YAExB,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,KAAK,CAAC,MAAM;YAClB,OAAO;gBACL,IAAI,GAAG,CAAC,MAAM;YAChB;QACF;QAEA,MAAM,UAAU;YACd,CAAC,YAAY,EAAE,IAAI,MAAM;YACzB,KAAK;YACL,cAAc,IAAI,CAAC,aAAa;YAChC;YACA,YAAY,IAAI,CAAC,WAAW;YAC5B,QAAQ;YACR,UAAU;YACV,MAAM;QACR;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,QAAQ;gBAAE;gBAAK;gBAAO;gBAAS;aAAG;QACvD,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,KAAK,UAAU;QAC7C;IACF;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,aAAa,OAAO,UAAU,CAAC;YAC/B,WAAW;QACb,OAAO;YACL,OAAO,SAAS;YAChB,aAAa,KAAK,MAAM;YACxB,WAAW,SAAS,QAAQ;QAC9B;QAEA,IAAI,aAAa,KAAK;YACpB,MAAM,IAAI,WAAW;QACvB;QAEA,MAAM,UAAU;YACd,CAAC,YAAY,EAAE;YACf,KAAK;YACL,cAAc,IAAI,CAAC,aAAa;YAChC;YACA,YAAY,IAAI,CAAC,WAAW;YAC5B,QAAQ;YACR;YACA,MAAM;QACR;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,QAAQ;gBAAE;gBAAM;gBAAO;gBAAS;aAAG;QACxD,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,MAAM,UAAU;QAC9C;IACF;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,aAAa,OAAO,UAAU,CAAC;YAC/B,WAAW;QACb,OAAO;YACL,OAAO,SAAS;YAChB,aAAa,KAAK,MAAM;YACxB,WAAW,SAAS,QAAQ;QAC9B;QAEA,IAAI,aAAa,KAAK;YACpB,MAAM,IAAI,WAAW;QACvB;QAEA,MAAM,UAAU;YACd,CAAC,YAAY,EAAE;YACf,KAAK;YACL,cAAc,IAAI,CAAC,aAAa;YAChC;YACA,YAAY,IAAI,CAAC,WAAW;YAC5B,QAAQ;YACR;YACA,MAAM;QACR;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,QAAQ;gBAAE;gBAAM;gBAAO;gBAAS;aAAG;QACxD,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,MAAM,UAAU;QAC9C;IACF;IAEA;;;;;;;;;;;;;;;GAeC,GACD,KAAK,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACtB,MAAM,oBAAoB,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC;QAC3E,IAAI,SAAS,QAAQ,MAAM,GAAG,IAAI;QAClC,IAAI,OAAO,QAAQ,QAAQ;QAE3B,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,aAAa,OAAO,UAAU,CAAC;YAC/B,WAAW;QACb,OAAO;YACL,OAAO,SAAS;YAChB,aAAa,KAAK,MAAM;YACxB,WAAW,SAAS,QAAQ;QAC9B;QAEA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,GAAG;YACtB,IACE,QACA,qBACA,kBAAkB,MAAM,CACtB,kBAAkB,SAAS,GACvB,+BACA,6BACL,EACD;gBACA,OAAO,cAAc,kBAAkB,UAAU;YACnD;YACA,IAAI,CAAC,SAAS,GAAG;QACnB,OAAO;YACL,OAAO;YACP,SAAS;QACX;QAEA,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,cAAc,GAAG;QAEvC,IAAI,mBAAmB;YACrB,MAAM,OAAO;gBACX,CAAC,YAAY,EAAE;gBACf,KAAK,QAAQ,GAAG;gBAChB,cAAc,IAAI,CAAC,aAAa;gBAChC,MAAM,QAAQ,IAAI;gBAClB,YAAY,IAAI,CAAC,WAAW;gBAC5B;gBACA;gBACA;YACF;YAEA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,OAAO,CAAC;oBAAC,IAAI,CAAC,QAAQ;oBAAE;oBAAM,IAAI,CAAC,SAAS;oBAAE;oBAAM;iBAAG;YAC9D,OAAO;gBACL,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,MAAM;YAC5C;QACF,OAAO;YACL,IAAI,CAAC,SAAS,CACZ,OAAO,KAAK,CAAC,MAAM;gBACjB,CAAC,YAAY,EAAE;gBACf,KAAK,QAAQ,GAAG;gBAChB,cAAc,IAAI,CAAC,aAAa;gBAChC,MAAM,QAAQ,IAAI;gBAClB,YAAY,IAAI,CAAC,WAAW;gBAC5B;gBACA;gBACA,MAAM;YACR,IACA;QAEJ;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,SAAS,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE;QACpC,IAAI,CAAC,UAAU;YACb,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,MAAM,UAAU;YAC5C;QACF;QAEA,MAAM,oBAAoB,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC;QAE3E,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,YAAY;QAC3C,IAAI,CAAC,UAAU,GAAG;QAClB,kBAAkB,QAAQ,CAAC,MAAM,QAAQ,GAAG,EAAE,CAAC,GAAG;YAChD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC1B,MAAM,MAAM,IAAI,MACd;gBAGF,IAAI,OAAO,OAAO,YAAY,GAAG;gBAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;oBAC3C,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC7B,MAAM,WAAW,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;oBAE1C,IAAI,OAAO,aAAa,YAAY,SAAS;gBAC/C;gBAEA;YACF;YAEA,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,YAAY;YAC3C,IAAI,CAAC,UAAU,GAAG;YAClB,QAAQ,QAAQ,GAAG;YACnB,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,KAAK,UAAU;YAC3C,IAAI,CAAC,OAAO;QACd;IACF;IAEA;;;;GAIC,GACD,UAAU;QACR,MAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE;YAC7C,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK;YAEhC,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC,YAAY;YAC7C,QAAQ,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,KAAK,CAAC;QAC9C;IACF;IAEA;;;;;GAKC,GACD,QAAQ,MAAM,EAAE;QACd,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC,YAAY;QAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACnB;IAEA;;;;;;GAMC,GACD,UAAU,IAAI,EAAE,EAAE,EAAE;QAClB,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,MAAM;QACrB,OAAO;YACL,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC9B;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4472, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/event-target.js"], "sourcesContent": ["'use strict';\n\nconst { kForOnEventAttribute, kListener } = require('./constants');\n\nconst kCode = Symbol('kCode');\nconst kData = Symbol('kData');\nconst kError = Symbol('kError');\nconst kMessage = Symbol('kMessage');\nconst kReason = Symbol('kReason');\nconst kTarget = Symbol('kTarget');\nconst kType = Symbol('kType');\nconst kWasClean = Symbol('kWasClean');\n\n/**\n * Class representing an event.\n */\nclass Event {\n  /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */\n  constructor(type) {\n    this[kTarget] = null;\n    this[kType] = type;\n  }\n\n  /**\n   * @type {*}\n   */\n  get target() {\n    return this[kTarget];\n  }\n\n  /**\n   * @type {String}\n   */\n  get type() {\n    return this[kType];\n  }\n}\n\nObject.defineProperty(Event.prototype, 'target', { enumerable: true });\nObject.defineProperty(Event.prototype, 'type', { enumerable: true });\n\n/**\n * Class representing a close event.\n *\n * @extends Event\n */\nclass CloseEvent extends Event {\n  /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kCode] = options.code === undefined ? 0 : options.code;\n    this[kReason] = options.reason === undefined ? '' : options.reason;\n    this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get code() {\n    return this[kCode];\n  }\n\n  /**\n   * @type {String}\n   */\n  get reason() {\n    return this[kReason];\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get wasClean() {\n    return this[kWasClean];\n  }\n}\n\nObject.defineProperty(CloseEvent.prototype, 'code', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'reason', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'wasClean', { enumerable: true });\n\n/**\n * Class representing an error event.\n *\n * @extends Event\n */\nclass ErrorEvent extends Event {\n  /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kError] = options.error === undefined ? null : options.error;\n    this[kMessage] = options.message === undefined ? '' : options.message;\n  }\n\n  /**\n   * @type {*}\n   */\n  get error() {\n    return this[kError];\n  }\n\n  /**\n   * @type {String}\n   */\n  get message() {\n    return this[kMessage];\n  }\n}\n\nObject.defineProperty(ErrorEvent.prototype, 'error', { enumerable: true });\nObject.defineProperty(ErrorEvent.prototype, 'message', { enumerable: true });\n\n/**\n * Class representing a message event.\n *\n * @extends Event\n */\nclass MessageEvent extends Event {\n  /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kData] = options.data === undefined ? null : options.data;\n  }\n\n  /**\n   * @type {*}\n   */\n  get data() {\n    return this[kData];\n  }\n}\n\nObject.defineProperty(MessageEvent.prototype, 'data', { enumerable: true });\n\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */\nconst EventTarget = {\n  /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */\n  addEventListener(type, handler, options = {}) {\n    for (const listener of this.listeners(type)) {\n      if (\n        !options[kForOnEventAttribute] &&\n        listener[kListener] === handler &&\n        !listener[kForOnEventAttribute]\n      ) {\n        return;\n      }\n    }\n\n    let wrapper;\n\n    if (type === 'message') {\n      wrapper = function onMessage(data, isBinary) {\n        const event = new MessageEvent('message', {\n          data: isBinary ? data : data.toString()\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'close') {\n      wrapper = function onClose(code, message) {\n        const event = new CloseEvent('close', {\n          code,\n          reason: message.toString(),\n          wasClean: this._closeFrameReceived && this._closeFrameSent\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'error') {\n      wrapper = function onError(error) {\n        const event = new ErrorEvent('error', {\n          error,\n          message: error.message\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'open') {\n      wrapper = function onOpen() {\n        const event = new Event('open');\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else {\n      return;\n    }\n\n    wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n    wrapper[kListener] = handler;\n\n    if (options.once) {\n      this.once(type, wrapper);\n    } else {\n      this.on(type, wrapper);\n    }\n  },\n\n  /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */\n  removeEventListener(type, handler) {\n    for (const listener of this.listeners(type)) {\n      if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n        this.removeListener(type, listener);\n        break;\n      }\n    }\n  }\n};\n\nmodule.exports = {\n  CloseEvent,\n  ErrorEvent,\n  Event,\n  EventTarget,\n  MessageEvent\n};\n\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */\nfunction callListener(listener, thisArg, event) {\n  if (typeof listener === 'object' && listener.handleEvent) {\n    listener.handleEvent.call(listener, event);\n  } else {\n    listener.call(thisArg, event);\n  }\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,oBAAoB,EAAE,SAAS,EAAE;AAEzC,MAAM,QAAQ,OAAO;AACrB,MAAM,QAAQ,OAAO;AACrB,MAAM,SAAS,OAAO;AACtB,MAAM,WAAW,OAAO;AACxB,MAAM,UAAU,OAAO;AACvB,MAAM,UAAU,OAAO;AACvB,MAAM,QAAQ,OAAO;AACrB,MAAM,YAAY,OAAO;AAEzB;;CAEC,GACD,MAAM;IACJ;;;;;GAKC,GACD,YAAY,IAAI,CAAE;QAChB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;GAEC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;AACF;AAEA,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,UAAU;IAAE,YAAY;AAAK;AACpE,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,QAAQ;IAAE,YAAY;AAAK;AAElE;;;;CAIC,GACD,MAAM,mBAAmB;IACvB;;;;;;;;;;;;GAYC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,KAAK,CAAC;QAEN,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,KAAK,YAAY,IAAI,QAAQ,IAAI;QAC3D,IAAI,CAAC,QAAQ,GAAG,QAAQ,MAAM,KAAK,YAAY,KAAK,QAAQ,MAAM;QAClE,IAAI,CAAC,UAAU,GAAG,QAAQ,QAAQ,KAAK,YAAY,QAAQ,QAAQ,QAAQ;IAC7E;IAEA;;GAEC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA;;GAEC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;GAEC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,UAAU;IACxB;AACF;AAEA,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,QAAQ;IAAE,YAAY;AAAK;AACvE,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,UAAU;IAAE,YAAY;AAAK;AACzE,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,YAAY;IAAE,YAAY;AAAK;AAE3E;;;;CAIC,GACD,MAAM,mBAAmB;IACvB;;;;;;;;GAQC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,KAAK,CAAC;QAEN,IAAI,CAAC,OAAO,GAAG,QAAQ,KAAK,KAAK,YAAY,OAAO,QAAQ,KAAK;QACjE,IAAI,CAAC,SAAS,GAAG,QAAQ,OAAO,KAAK,YAAY,KAAK,QAAQ,OAAO;IACvE;IAEA;;GAEC,GACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA;;GAEC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS;IACvB;AACF;AAEA,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,SAAS;IAAE,YAAY;AAAK;AACxE,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,WAAW;IAAE,YAAY;AAAK;AAE1E;;;;CAIC,GACD,MAAM,qBAAqB;IACzB;;;;;;;GAOC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,KAAK,CAAC;QAEN,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,KAAK,YAAY,OAAO,QAAQ,IAAI;IAChE;IAEA;;GAEC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;AACF;AAEA,OAAO,cAAc,CAAC,aAAa,SAAS,EAAE,QAAQ;IAAE,YAAY;AAAK;AAEzE;;;;;CAKC,GACD,MAAM,cAAc;IAClB;;;;;;;;;;;GAWC,GACD,kBAAiB,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1C,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,MAAO;YAC3C,IACE,CAAC,OAAO,CAAC,qBAAqB,IAC9B,QAAQ,CAAC,UAAU,KAAK,WACxB,CAAC,QAAQ,CAAC,qBAAqB,EAC/B;gBACA;YACF;QACF;QAEA,IAAI;QAEJ,IAAI,SAAS,WAAW;YACtB,UAAU,SAAS,UAAU,IAAI,EAAE,QAAQ;gBACzC,MAAM,QAAQ,IAAI,aAAa,WAAW;oBACxC,MAAM,WAAW,OAAO,KAAK,QAAQ;gBACvC;gBAEA,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO,IAAI,SAAS,SAAS;YAC3B,UAAU,SAAS,QAAQ,IAAI,EAAE,OAAO;gBACtC,MAAM,QAAQ,IAAI,WAAW,SAAS;oBACpC;oBACA,QAAQ,QAAQ,QAAQ;oBACxB,UAAU,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe;gBAC5D;gBAEA,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO,IAAI,SAAS,SAAS;YAC3B,UAAU,SAAS,QAAQ,KAAK;gBAC9B,MAAM,QAAQ,IAAI,WAAW,SAAS;oBACpC;oBACA,SAAS,MAAM,OAAO;gBACxB;gBAEA,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO,IAAI,SAAS,QAAQ;YAC1B,UAAU,SAAS;gBACjB,MAAM,QAAQ,IAAI,MAAM;gBAExB,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO;YACL;QACF;QAEA,OAAO,CAAC,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB;QAC/D,OAAO,CAAC,UAAU,GAAG;QAErB,IAAI,QAAQ,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,MAAM;QAClB,OAAO;YACL,IAAI,CAAC,EAAE,CAAC,MAAM;QAChB;IACF;IAEA;;;;;;GAMC,GACD,qBAAoB,IAAI,EAAE,OAAO;QAC/B,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,MAAO;YAC3C,IAAI,QAAQ,CAAC,UAAU,KAAK,WAAW,CAAC,QAAQ,CAAC,qBAAqB,EAAE;gBACtE,IAAI,CAAC,cAAc,CAAC,MAAM;gBAC1B;YACF;QACF;IACF;AACF;AAEA,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,QAAQ,EAAE,OAAO,EAAE,KAAK;IAC5C,IAAI,OAAO,aAAa,YAAY,SAAS,WAAW,EAAE;QACxD,SAAS,WAAW,CAAC,IAAI,CAAC,UAAU;IACtC,OAAO;QACL,SAAS,IAAI,CAAC,SAAS;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4728, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/extension.js"], "sourcesContent": ["'use strict';\n\nconst { tokenChars } = require('./validation');\n\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */\nfunction push(dest, name, elem) {\n  if (dest[name] === undefined) dest[name] = [elem];\n  else dest[name].push(elem);\n}\n\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */\nfunction parse(header) {\n  const offers = Object.create(null);\n  let params = Object.create(null);\n  let mustUnescape = false;\n  let isEscaping = false;\n  let inQuotes = false;\n  let extensionName;\n  let paramName;\n  let start = -1;\n  let code = -1;\n  let end = -1;\n  let i = 0;\n\n  for (; i < header.length; i++) {\n    code = header.charCodeAt(i);\n\n    if (extensionName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (\n        i !== 0 &&\n        (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */\n      ) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b /* ';' */ || code === 0x2c /* ',' */) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        const name = header.slice(start, end);\n        if (code === 0x2c) {\n          push(offers, name, params);\n          params = Object.create(null);\n        } else {\n          extensionName = name;\n        }\n\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else if (paramName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (code === 0x20 || code === 0x09) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        push(params, header.slice(start, end), true);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        start = end = -1;\n      } else if (code === 0x3d /* '=' */ && start !== -1 && end === -1) {\n        paramName = header.slice(start, i);\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else {\n      //\n      // The value of a quoted-string after unescaping must conform to the\n      // token ABNF, so only token characters are valid.\n      // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n      //\n      if (isEscaping) {\n        if (tokenChars[code] !== 1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (start === -1) start = i;\n        else if (!mustUnescape) mustUnescape = true;\n        isEscaping = false;\n      } else if (inQuotes) {\n        if (tokenChars[code] === 1) {\n          if (start === -1) start = i;\n        } else if (code === 0x22 /* '\"' */ && start !== -1) {\n          inQuotes = false;\n          end = i;\n        } else if (code === 0x5c /* '\\' */) {\n          isEscaping = true;\n        } else {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n      } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n        inQuotes = true;\n      } else if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n        if (end === -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        let value = header.slice(start, end);\n        if (mustUnescape) {\n          value = value.replace(/\\\\/g, '');\n          mustUnescape = false;\n        }\n        push(params, paramName, value);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        paramName = undefined;\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    }\n  }\n\n  if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n\n  if (end === -1) end = i;\n  const token = header.slice(start, end);\n  if (extensionName === undefined) {\n    push(offers, token, params);\n  } else {\n    if (paramName === undefined) {\n      push(params, token, true);\n    } else if (mustUnescape) {\n      push(params, paramName, token.replace(/\\\\/g, ''));\n    } else {\n      push(params, paramName, token);\n    }\n    push(offers, extensionName, params);\n  }\n\n  return offers;\n}\n\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */\nfunction format(extensions) {\n  return Object.keys(extensions)\n    .map((extension) => {\n      let configurations = extensions[extension];\n      if (!Array.isArray(configurations)) configurations = [configurations];\n      return configurations\n        .map((params) => {\n          return [extension]\n            .concat(\n              Object.keys(params).map((k) => {\n                let values = params[k];\n                if (!Array.isArray(values)) values = [values];\n                return values\n                  .map((v) => (v === true ? k : `${k}=${v}`))\n                  .join('; ');\n              })\n            )\n            .join('; ');\n        })\n        .join(', ');\n    })\n    .join(', ');\n}\n\nmodule.exports = { format, parse };\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,UAAU,EAAE;AAEpB;;;;;;;;;CASC,GACD,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI;IAC5B,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,KAAK,GAAG;QAAC;KAAK;SAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACvB;AAEA;;;;;;CAMC,GACD,SAAS,MAAM,MAAM;IACnB,MAAM,SAAS,OAAO,MAAM,CAAC;IAC7B,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,IAAI,eAAe;IACnB,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI;IACJ,IAAI;IACJ,IAAI,QAAQ,CAAC;IACb,IAAI,OAAO,CAAC;IACZ,IAAI,MAAM,CAAC;IACX,IAAI,IAAI;IAER,MAAO,IAAI,OAAO,MAAM,EAAE,IAAK;QAC7B,OAAO,OAAO,UAAU,CAAC;QAEzB,IAAI,kBAAkB,WAAW;YAC/B,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,GAAG;gBACxC,IAAI,UAAU,CAAC,GAAG,QAAQ;YAC5B,OAAO,IACL,MAAM,KACN,CAAC,SAAS,KAAK,OAAO,OAAM,SAAS,IAAI,GACzC;gBACA,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,GAAG,MAAM;YACxC,OAAO,IAAI,SAAS,KAAK,OAAO,OAAM,SAAS,KAAK,OAAO,KAAI;gBAC7D,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;gBACtB,MAAM,OAAO,OAAO,KAAK,CAAC,OAAO;gBACjC,IAAI,SAAS,MAAM;oBACjB,KAAK,QAAQ,MAAM;oBACnB,SAAS,OAAO,MAAM,CAAC;gBACzB,OAAO;oBACL,gBAAgB;gBAClB;gBAEA,QAAQ,MAAM,CAAC;YACjB,OAAO;gBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;YAC5D;QACF,OAAO,IAAI,cAAc,WAAW;YAClC,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,GAAG;gBACxC,IAAI,UAAU,CAAC,GAAG,QAAQ;YAC5B,OAAO,IAAI,SAAS,QAAQ,SAAS,MAAM;gBACzC,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,GAAG,MAAM;YACxC,OAAO,IAAI,SAAS,QAAQ,SAAS,MAAM;gBACzC,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;gBACtB,KAAK,QAAQ,OAAO,KAAK,CAAC,OAAO,MAAM;gBACvC,IAAI,SAAS,MAAM;oBACjB,KAAK,QAAQ,eAAe;oBAC5B,SAAS,OAAO,MAAM,CAAC;oBACvB,gBAAgB;gBAClB;gBAEA,QAAQ,MAAM,CAAC;YACjB,OAAO,IAAI,SAAS,KAAK,OAAO,OAAM,UAAU,CAAC,KAAK,QAAQ,CAAC,GAAG;gBAChE,YAAY,OAAO,KAAK,CAAC,OAAO;gBAChC,QAAQ,MAAM,CAAC;YACjB,OAAO;gBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;YAC5D;QACF,OAAO;YACL,EAAE;YACF,oEAAoE;YACpE,kDAAkD;YAClD,uDAAuD;YACvD,EAAE;YACF,IAAI,YAAY;gBACd,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG;oBAC1B,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBACA,IAAI,UAAU,CAAC,GAAG,QAAQ;qBACrB,IAAI,CAAC,cAAc,eAAe;gBACvC,aAAa;YACf,OAAO,IAAI,UAAU;gBACnB,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG;oBAC1B,IAAI,UAAU,CAAC,GAAG,QAAQ;gBAC5B,OAAO,IAAI,SAAS,KAAK,OAAO,OAAM,UAAU,CAAC,GAAG;oBAClD,WAAW;oBACX,MAAM;gBACR,OAAO,IAAI,SAAS,KAAK,OAAO,KAAI;oBAClC,aAAa;gBACf,OAAO;oBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;YACF,OAAO,IAAI,SAAS,QAAQ,OAAO,UAAU,CAAC,IAAI,OAAO,MAAM;gBAC7D,WAAW;YACb,OAAO,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,GAAG;gBAC/C,IAAI,UAAU,CAAC,GAAG,QAAQ;YAC5B,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,QAAQ,SAAS,IAAI,GAAG;gBAC3D,IAAI,QAAQ,CAAC,GAAG,MAAM;YACxB,OAAO,IAAI,SAAS,QAAQ,SAAS,MAAM;gBACzC,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;gBACtB,IAAI,QAAQ,OAAO,KAAK,CAAC,OAAO;gBAChC,IAAI,cAAc;oBAChB,QAAQ,MAAM,OAAO,CAAC,OAAO;oBAC7B,eAAe;gBACjB;gBACA,KAAK,QAAQ,WAAW;gBACxB,IAAI,SAAS,MAAM;oBACjB,KAAK,QAAQ,eAAe;oBAC5B,SAAS,OAAO,MAAM,CAAC;oBACvB,gBAAgB;gBAClB;gBAEA,YAAY;gBACZ,QAAQ,MAAM,CAAC;YACjB,OAAO;gBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;YAC5D;QACF;IACF;IAEA,IAAI,UAAU,CAAC,KAAK,YAAY,SAAS,QAAQ,SAAS,MAAM;QAC9D,MAAM,IAAI,YAAY;IACxB;IAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;IACtB,MAAM,QAAQ,OAAO,KAAK,CAAC,OAAO;IAClC,IAAI,kBAAkB,WAAW;QAC/B,KAAK,QAAQ,OAAO;IACtB,OAAO;QACL,IAAI,cAAc,WAAW;YAC3B,KAAK,QAAQ,OAAO;QACtB,OAAO,IAAI,cAAc;YACvB,KAAK,QAAQ,WAAW,MAAM,OAAO,CAAC,OAAO;QAC/C,OAAO;YACL,KAAK,QAAQ,WAAW;QAC1B;QACA,KAAK,QAAQ,eAAe;IAC9B;IAEA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,OAAO,UAAU;IACxB,OAAO,OAAO,IAAI,CAAC,YAChB,GAAG,CAAC,CAAC;QACJ,IAAI,iBAAiB,UAAU,CAAC,UAAU;QAC1C,IAAI,CAAC,MAAM,OAAO,CAAC,iBAAiB,iBAAiB;YAAC;SAAe;QACrE,OAAO,eACJ,GAAG,CAAC,CAAC;YACJ,OAAO;gBAAC;aAAU,CACf,MAAM,CACL,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACvB,IAAI,SAAS,MAAM,CAAC,EAAE;gBACtB,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,SAAS;oBAAC;iBAAO;gBAC7C,OAAO,OACJ,GAAG,CAAC,CAAC,IAAO,MAAM,OAAO,IAAI,GAAG,EAAE,CAAC,EAAE,GAAG,EACxC,IAAI,CAAC;YACV,IAED,IAAI,CAAC;QACV,GACC,IAAI,CAAC;IACV,GACC,IAAI,CAAC;AACV;AAEA,OAAO,OAAO,GAAG;IAAE;IAAQ;AAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4915, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/DATACOIN/datacoin-token/dapp-ui/node_modules/ws/lib/websocket.js"], "sourcesContent": ["/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */\n\n'use strict';\n\nconst EventEmitter = require('events');\nconst https = require('https');\nconst http = require('http');\nconst net = require('net');\nconst tls = require('tls');\nconst { randomBytes, createHash } = require('crypto');\nconst { Duplex, Readable } = require('stream');\nconst { URL } = require('url');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst Receiver = require('./receiver');\nconst Sender = require('./sender');\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  GUID,\n  kForOnEventAttribute,\n  kListener,\n  kStatusCode,\n  kWebSocket,\n  NOOP\n} = require('./constants');\nconst {\n  EventTarget: { addEventListener, removeEventListener }\n} = require('./event-target');\nconst { format, parse } = require('./extension');\nconst { toBuffer } = require('./buffer-util');\n\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol('kAborted');\nconst protocolVersions = [8, 13];\nconst readyStates = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */\nclass WebSocket extends EventEmitter {\n  /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */\n  constructor(address, protocols, options) {\n    super();\n\n    this._binaryType = BINARY_TYPES[0];\n    this._closeCode = 1006;\n    this._closeFrameReceived = false;\n    this._closeFrameSent = false;\n    this._closeMessage = EMPTY_BUFFER;\n    this._closeTimer = null;\n    this._extensions = {};\n    this._paused = false;\n    this._protocol = '';\n    this._readyState = WebSocket.CONNECTING;\n    this._receiver = null;\n    this._sender = null;\n    this._socket = null;\n\n    if (address !== null) {\n      this._bufferedAmount = 0;\n      this._isServer = false;\n      this._redirects = 0;\n\n      if (protocols === undefined) {\n        protocols = [];\n      } else if (!Array.isArray(protocols)) {\n        if (typeof protocols === 'object' && protocols !== null) {\n          options = protocols;\n          protocols = [];\n        } else {\n          protocols = [protocols];\n        }\n      }\n\n      initAsClient(this, address, protocols, options);\n    } else {\n      this._autoPong = options.autoPong;\n      this._isServer = true;\n    }\n  }\n\n  /**\n   * This deviates from the WHATWG interface since ws doesn't support the\n   * required default \"blob\" type (instead we define a custom \"nodebuffer\"\n   * type).\n   *\n   * @type {String}\n   */\n  get binaryType() {\n    return this._binaryType;\n  }\n\n  set binaryType(type) {\n    if (!BINARY_TYPES.includes(type)) return;\n\n    this._binaryType = type;\n\n    //\n    // Allow to change `binaryType` on the fly.\n    //\n    if (this._receiver) this._receiver._binaryType = type;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get bufferedAmount() {\n    if (!this._socket) return this._bufferedAmount;\n\n    return this._socket._writableState.length + this._sender._bufferedBytes;\n  }\n\n  /**\n   * @type {String}\n   */\n  get extensions() {\n    return Object.keys(this._extensions).join();\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get isPaused() {\n    return this._paused;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onclose() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onerror() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onopen() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onmessage() {\n    return null;\n  }\n\n  /**\n   * @type {String}\n   */\n  get protocol() {\n    return this._protocol;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get readyState() {\n    return this._readyState;\n  }\n\n  /**\n   * @type {String}\n   */\n  get url() {\n    return this._url;\n  }\n\n  /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */\n  setSocket(socket, head, options) {\n    const receiver = new Receiver({\n      allowSynchronousEvents: options.allowSynchronousEvents,\n      binaryType: this.binaryType,\n      extensions: this._extensions,\n      isServer: this._isServer,\n      maxPayload: options.maxPayload,\n      skipUTF8Validation: options.skipUTF8Validation\n    });\n\n    this._sender = new Sender(socket, this._extensions, options.generateMask);\n    this._receiver = receiver;\n    this._socket = socket;\n\n    receiver[kWebSocket] = this;\n    socket[kWebSocket] = this;\n\n    receiver.on('conclude', receiverOnConclude);\n    receiver.on('drain', receiverOnDrain);\n    receiver.on('error', receiverOnError);\n    receiver.on('message', receiverOnMessage);\n    receiver.on('ping', receiverOnPing);\n    receiver.on('pong', receiverOnPong);\n\n    //\n    // These methods may not be available if `socket` is just a `Duplex`.\n    //\n    if (socket.setTimeout) socket.setTimeout(0);\n    if (socket.setNoDelay) socket.setNoDelay();\n\n    if (head.length > 0) socket.unshift(head);\n\n    socket.on('close', socketOnClose);\n    socket.on('data', socketOnData);\n    socket.on('end', socketOnEnd);\n    socket.on('error', socketOnError);\n\n    this._readyState = WebSocket.OPEN;\n    this.emit('open');\n  }\n\n  /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */\n  emitClose() {\n    if (!this._socket) {\n      this._readyState = WebSocket.CLOSED;\n      this.emit('close', this._closeCode, this._closeMessage);\n      return;\n    }\n\n    if (this._extensions[PerMessageDeflate.extensionName]) {\n      this._extensions[PerMessageDeflate.extensionName].cleanup();\n    }\n\n    this._receiver.removeAllListeners();\n    this._readyState = WebSocket.CLOSED;\n    this.emit('close', this._closeCode, this._closeMessage);\n  }\n\n  /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */\n  close(code, data) {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this.readyState === WebSocket.CLOSING) {\n      if (\n        this._closeFrameSent &&\n        (this._closeFrameReceived || this._receiver._writableState.errorEmitted)\n      ) {\n        this._socket.end();\n      }\n\n      return;\n    }\n\n    this._readyState = WebSocket.CLOSING;\n    this._sender.close(code, data, !this._isServer, (err) => {\n      //\n      // This error is handled by the `'error'` listener on the socket. We only\n      // want to know if the close frame has been sent here.\n      //\n      if (err) return;\n\n      this._closeFrameSent = true;\n\n      if (\n        this._closeFrameReceived ||\n        this._receiver._writableState.errorEmitted\n      ) {\n        this._socket.end();\n      }\n    });\n\n    //\n    // Specify a timeout for the closing handshake to complete.\n    //\n    this._closeTimer = setTimeout(\n      this._socket.destroy.bind(this._socket),\n      closeTimeout\n    );\n  }\n\n  /**\n   * Pause the socket.\n   *\n   * @public\n   */\n  pause() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = true;\n    this._socket.pause();\n  }\n\n  /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */\n  ping(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */\n  pong(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Resume the socket.\n   *\n   * @public\n   */\n  resume() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = false;\n    if (!this._receiver._writableState.needDrain) this._socket.resume();\n  }\n\n  /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */\n  send(data, options, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof options === 'function') {\n      cb = options;\n      options = {};\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    const opts = {\n      binary: typeof data !== 'string',\n      mask: !this._isServer,\n      compress: true,\n      fin: true,\n      ...options\n    };\n\n    if (!this._extensions[PerMessageDeflate.extensionName]) {\n      opts.compress = false;\n    }\n\n    this._sender.send(data || EMPTY_BUFFER, opts, cb);\n  }\n\n  /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */\n  terminate() {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this._socket) {\n      this._readyState = WebSocket.CLOSING;\n      this._socket.destroy();\n    }\n  }\n}\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n[\n  'binaryType',\n  'bufferedAmount',\n  'extensions',\n  'isPaused',\n  'protocol',\n  'readyState',\n  'url'\n].forEach((property) => {\n  Object.defineProperty(WebSocket.prototype, property, { enumerable: true });\n});\n\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n['open', 'error', 'close', 'message'].forEach((method) => {\n  Object.defineProperty(WebSocket.prototype, `on${method}`, {\n    enumerable: true,\n    get() {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) return listener[kListener];\n      }\n\n      return null;\n    },\n    set(handler) {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) {\n          this.removeListener(method, listener);\n          break;\n        }\n      }\n\n      if (typeof handler !== 'function') return;\n\n      this.addEventListener(method, handler, {\n        [kForOnEventAttribute]: true\n      });\n    }\n  });\n});\n\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\n\nmodule.exports = WebSocket;\n\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */\nfunction initAsClient(websocket, address, protocols, options) {\n  const opts = {\n    allowSynchronousEvents: true,\n    autoPong: true,\n    protocolVersion: protocolVersions[1],\n    maxPayload: 100 * 1024 * 1024,\n    skipUTF8Validation: false,\n    perMessageDeflate: true,\n    followRedirects: false,\n    maxRedirects: 10,\n    ...options,\n    socketPath: undefined,\n    hostname: undefined,\n    protocol: undefined,\n    timeout: undefined,\n    method: 'GET',\n    host: undefined,\n    path: undefined,\n    port: undefined\n  };\n\n  websocket._autoPong = opts.autoPong;\n\n  if (!protocolVersions.includes(opts.protocolVersion)) {\n    throw new RangeError(\n      `Unsupported protocol version: ${opts.protocolVersion} ` +\n        `(supported versions: ${protocolVersions.join(', ')})`\n    );\n  }\n\n  let parsedUrl;\n\n  if (address instanceof URL) {\n    parsedUrl = address;\n  } else {\n    try {\n      parsedUrl = new URL(address);\n    } catch (e) {\n      throw new SyntaxError(`Invalid URL: ${address}`);\n    }\n  }\n\n  if (parsedUrl.protocol === 'http:') {\n    parsedUrl.protocol = 'ws:';\n  } else if (parsedUrl.protocol === 'https:') {\n    parsedUrl.protocol = 'wss:';\n  }\n\n  websocket._url = parsedUrl.href;\n\n  const isSecure = parsedUrl.protocol === 'wss:';\n  const isIpcUrl = parsedUrl.protocol === 'ws+unix:';\n  let invalidUrlMessage;\n\n  if (parsedUrl.protocol !== 'ws:' && !isSecure && !isIpcUrl) {\n    invalidUrlMessage =\n      'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' +\n      '\"http:\", \"https\", or \"ws+unix:\"';\n  } else if (isIpcUrl && !parsedUrl.pathname) {\n    invalidUrlMessage = \"The URL's pathname is empty\";\n  } else if (parsedUrl.hash) {\n    invalidUrlMessage = 'The URL contains a fragment identifier';\n  }\n\n  if (invalidUrlMessage) {\n    const err = new SyntaxError(invalidUrlMessage);\n\n    if (websocket._redirects === 0) {\n      throw err;\n    } else {\n      emitErrorAndClose(websocket, err);\n      return;\n    }\n  }\n\n  const defaultPort = isSecure ? 443 : 80;\n  const key = randomBytes(16).toString('base64');\n  const request = isSecure ? https.request : http.request;\n  const protocolSet = new Set();\n  let perMessageDeflate;\n\n  opts.createConnection =\n    opts.createConnection || (isSecure ? tlsConnect : netConnect);\n  opts.defaultPort = opts.defaultPort || defaultPort;\n  opts.port = parsedUrl.port || defaultPort;\n  opts.host = parsedUrl.hostname.startsWith('[')\n    ? parsedUrl.hostname.slice(1, -1)\n    : parsedUrl.hostname;\n  opts.headers = {\n    ...opts.headers,\n    'Sec-WebSocket-Version': opts.protocolVersion,\n    'Sec-WebSocket-Key': key,\n    Connection: 'Upgrade',\n    Upgrade: 'websocket'\n  };\n  opts.path = parsedUrl.pathname + parsedUrl.search;\n  opts.timeout = opts.handshakeTimeout;\n\n  if (opts.perMessageDeflate) {\n    perMessageDeflate = new PerMessageDeflate(\n      opts.perMessageDeflate !== true ? opts.perMessageDeflate : {},\n      false,\n      opts.maxPayload\n    );\n    opts.headers['Sec-WebSocket-Extensions'] = format({\n      [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n    });\n  }\n  if (protocols.length) {\n    for (const protocol of protocols) {\n      if (\n        typeof protocol !== 'string' ||\n        !subprotocolRegex.test(protocol) ||\n        protocolSet.has(protocol)\n      ) {\n        throw new SyntaxError(\n          'An invalid or duplicated subprotocol was specified'\n        );\n      }\n\n      protocolSet.add(protocol);\n    }\n\n    opts.headers['Sec-WebSocket-Protocol'] = protocols.join(',');\n  }\n  if (opts.origin) {\n    if (opts.protocolVersion < 13) {\n      opts.headers['Sec-WebSocket-Origin'] = opts.origin;\n    } else {\n      opts.headers.Origin = opts.origin;\n    }\n  }\n  if (parsedUrl.username || parsedUrl.password) {\n    opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n  }\n\n  if (isIpcUrl) {\n    const parts = opts.path.split(':');\n\n    opts.socketPath = parts[0];\n    opts.path = parts[1];\n  }\n\n  let req;\n\n  if (opts.followRedirects) {\n    if (websocket._redirects === 0) {\n      websocket._originalIpc = isIpcUrl;\n      websocket._originalSecure = isSecure;\n      websocket._originalHostOrSocketPath = isIpcUrl\n        ? opts.socketPath\n        : parsedUrl.host;\n\n      const headers = options && options.headers;\n\n      //\n      // Shallow copy the user provided options so that headers can be changed\n      // without mutating the original object.\n      //\n      options = { ...options, headers: {} };\n\n      if (headers) {\n        for (const [key, value] of Object.entries(headers)) {\n          options.headers[key.toLowerCase()] = value;\n        }\n      }\n    } else if (websocket.listenerCount('redirect') === 0) {\n      const isSameHost = isIpcUrl\n        ? websocket._originalIpc\n          ? opts.socketPath === websocket._originalHostOrSocketPath\n          : false\n        : websocket._originalIpc\n          ? false\n          : parsedUrl.host === websocket._originalHostOrSocketPath;\n\n      if (!isSameHost || (websocket._originalSecure && !isSecure)) {\n        //\n        // Match curl 7.77.0 behavior and drop the following headers. These\n        // headers are also dropped when following a redirect to a subdomain.\n        //\n        delete opts.headers.authorization;\n        delete opts.headers.cookie;\n\n        if (!isSameHost) delete opts.headers.host;\n\n        opts.auth = undefined;\n      }\n    }\n\n    //\n    // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n    // If the `Authorization` header is set, then there is nothing to do as it\n    // will take precedence.\n    //\n    if (opts.auth && !options.headers.authorization) {\n      options.headers.authorization =\n        'Basic ' + Buffer.from(opts.auth).toString('base64');\n    }\n\n    req = websocket._req = request(opts);\n\n    if (websocket._redirects) {\n      //\n      // Unlike what is done for the `'upgrade'` event, no early exit is\n      // triggered here if the user calls `websocket.close()` or\n      // `websocket.terminate()` from a listener of the `'redirect'` event. This\n      // is because the user can also call `request.destroy()` with an error\n      // before calling `websocket.close()` or `websocket.terminate()` and this\n      // would result in an error being emitted on the `request` object with no\n      // `'error'` event listeners attached.\n      //\n      websocket.emit('redirect', websocket.url, req);\n    }\n  } else {\n    req = websocket._req = request(opts);\n  }\n\n  if (opts.timeout) {\n    req.on('timeout', () => {\n      abortHandshake(websocket, req, 'Opening handshake has timed out');\n    });\n  }\n\n  req.on('error', (err) => {\n    if (req === null || req[kAborted]) return;\n\n    req = websocket._req = null;\n    emitErrorAndClose(websocket, err);\n  });\n\n  req.on('response', (res) => {\n    const location = res.headers.location;\n    const statusCode = res.statusCode;\n\n    if (\n      location &&\n      opts.followRedirects &&\n      statusCode >= 300 &&\n      statusCode < 400\n    ) {\n      if (++websocket._redirects > opts.maxRedirects) {\n        abortHandshake(websocket, req, 'Maximum redirects exceeded');\n        return;\n      }\n\n      req.abort();\n\n      let addr;\n\n      try {\n        addr = new URL(location, address);\n      } catch (e) {\n        const err = new SyntaxError(`Invalid URL: ${location}`);\n        emitErrorAndClose(websocket, err);\n        return;\n      }\n\n      initAsClient(websocket, addr, protocols, options);\n    } else if (!websocket.emit('unexpected-response', req, res)) {\n      abortHandshake(\n        websocket,\n        req,\n        `Unexpected server response: ${res.statusCode}`\n      );\n    }\n  });\n\n  req.on('upgrade', (res, socket, head) => {\n    websocket.emit('upgrade', res);\n\n    //\n    // The user may have closed the connection from a listener of the\n    // `'upgrade'` event.\n    //\n    if (websocket.readyState !== WebSocket.CONNECTING) return;\n\n    req = websocket._req = null;\n\n    const upgrade = res.headers.upgrade;\n\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      abortHandshake(websocket, socket, 'Invalid Upgrade header');\n      return;\n    }\n\n    const digest = createHash('sha1')\n      .update(key + GUID)\n      .digest('base64');\n\n    if (res.headers['sec-websocket-accept'] !== digest) {\n      abortHandshake(websocket, socket, 'Invalid Sec-WebSocket-Accept header');\n      return;\n    }\n\n    const serverProt = res.headers['sec-websocket-protocol'];\n    let protError;\n\n    if (serverProt !== undefined) {\n      if (!protocolSet.size) {\n        protError = 'Server sent a subprotocol but none was requested';\n      } else if (!protocolSet.has(serverProt)) {\n        protError = 'Server sent an invalid subprotocol';\n      }\n    } else if (protocolSet.size) {\n      protError = 'Server sent no subprotocol';\n    }\n\n    if (protError) {\n      abortHandshake(websocket, socket, protError);\n      return;\n    }\n\n    if (serverProt) websocket._protocol = serverProt;\n\n    const secWebSocketExtensions = res.headers['sec-websocket-extensions'];\n\n    if (secWebSocketExtensions !== undefined) {\n      if (!perMessageDeflate) {\n        const message =\n          'Server sent a Sec-WebSocket-Extensions header but no extension ' +\n          'was requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      let extensions;\n\n      try {\n        extensions = parse(secWebSocketExtensions);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      const extensionNames = Object.keys(extensions);\n\n      if (\n        extensionNames.length !== 1 ||\n        extensionNames[0] !== PerMessageDeflate.extensionName\n      ) {\n        const message = 'Server indicated an extension that was not requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      try {\n        perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      websocket._extensions[PerMessageDeflate.extensionName] =\n        perMessageDeflate;\n    }\n\n    websocket.setSocket(socket, head, {\n      allowSynchronousEvents: opts.allowSynchronousEvents,\n      generateMask: opts.generateMask,\n      maxPayload: opts.maxPayload,\n      skipUTF8Validation: opts.skipUTF8Validation\n    });\n  });\n\n  if (opts.finishRequest) {\n    opts.finishRequest(req, websocket);\n  } else {\n    req.end();\n  }\n}\n\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */\nfunction emitErrorAndClose(websocket, err) {\n  websocket._readyState = WebSocket.CLOSING;\n  websocket.emit('error', err);\n  websocket.emitClose();\n}\n\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */\nfunction netConnect(options) {\n  options.path = options.socketPath;\n  return net.connect(options);\n}\n\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */\nfunction tlsConnect(options) {\n  options.path = undefined;\n\n  if (!options.servername && options.servername !== '') {\n    options.servername = net.isIP(options.host) ? '' : options.host;\n  }\n\n  return tls.connect(options);\n}\n\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */\nfunction abortHandshake(websocket, stream, message) {\n  websocket._readyState = WebSocket.CLOSING;\n\n  const err = new Error(message);\n  Error.captureStackTrace(err, abortHandshake);\n\n  if (stream.setHeader) {\n    stream[kAborted] = true;\n    stream.abort();\n\n    if (stream.socket && !stream.socket.destroyed) {\n      //\n      // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n      // called after the request completed. See\n      // https://github.com/websockets/ws/issues/1869.\n      //\n      stream.socket.destroy();\n    }\n\n    process.nextTick(emitErrorAndClose, websocket, err);\n  } else {\n    stream.destroy(err);\n    stream.once('error', websocket.emit.bind(websocket, 'error'));\n    stream.once('close', websocket.emitClose.bind(websocket));\n  }\n}\n\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */\nfunction sendAfterClose(websocket, data, cb) {\n  if (data) {\n    const length = toBuffer(data).length;\n\n    //\n    // The `_bufferedAmount` property is used only when the peer is a client and\n    // the opening handshake fails. Under these circumstances, in fact, the\n    // `setSocket()` method is not called, so the `_socket` and `_sender`\n    // properties are set to `null`.\n    //\n    if (websocket._socket) websocket._sender._bufferedBytes += length;\n    else websocket._bufferedAmount += length;\n  }\n\n  if (cb) {\n    const err = new Error(\n      `WebSocket is not open: readyState ${websocket.readyState} ` +\n        `(${readyStates[websocket.readyState]})`\n    );\n    process.nextTick(cb, err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */\nfunction receiverOnConclude(code, reason) {\n  const websocket = this[kWebSocket];\n\n  websocket._closeFrameReceived = true;\n  websocket._closeMessage = reason;\n  websocket._closeCode = code;\n\n  if (websocket._socket[kWebSocket] === undefined) return;\n\n  websocket._socket.removeListener('data', socketOnData);\n  process.nextTick(resume, websocket._socket);\n\n  if (code === 1005) websocket.close();\n  else websocket.close(code, reason);\n}\n\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */\nfunction receiverOnDrain() {\n  const websocket = this[kWebSocket];\n\n  if (!websocket.isPaused) websocket._socket.resume();\n}\n\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */\nfunction receiverOnError(err) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._socket[kWebSocket] !== undefined) {\n    websocket._socket.removeListener('data', socketOnData);\n\n    //\n    // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n    // https://github.com/websockets/ws/issues/1940.\n    //\n    process.nextTick(resume, websocket._socket);\n\n    websocket.close(err[kStatusCode]);\n  }\n\n  websocket.emit('error', err);\n}\n\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */\nfunction receiverOnFinish() {\n  this[kWebSocket].emitClose();\n}\n\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */\nfunction receiverOnMessage(data, isBinary) {\n  this[kWebSocket].emit('message', data, isBinary);\n}\n\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */\nfunction receiverOnPing(data) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n  websocket.emit('ping', data);\n}\n\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */\nfunction receiverOnPong(data) {\n  this[kWebSocket].emit('pong', data);\n}\n\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */\nfunction resume(stream) {\n  stream.resume();\n}\n\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */\nfunction socketOnClose() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('close', socketOnClose);\n  this.removeListener('data', socketOnData);\n  this.removeListener('end', socketOnEnd);\n\n  websocket._readyState = WebSocket.CLOSING;\n\n  let chunk;\n\n  //\n  // The close frame might not have been received or the `'end'` event emitted,\n  // for example, if the socket was destroyed due to an error. Ensure that the\n  // `receiver` stream is closed after writing any remaining buffered data to\n  // it. If the readable side of the socket is in flowing mode then there is no\n  // buffered data as everything has been already written and `readable.read()`\n  // will return `null`. If instead, the socket is paused, any possible buffered\n  // data will be read as a single chunk.\n  //\n  if (\n    !this._readableState.endEmitted &&\n    !websocket._closeFrameReceived &&\n    !websocket._receiver._writableState.errorEmitted &&\n    (chunk = websocket._socket.read()) !== null\n  ) {\n    websocket._receiver.write(chunk);\n  }\n\n  websocket._receiver.end();\n\n  this[kWebSocket] = undefined;\n\n  clearTimeout(websocket._closeTimer);\n\n  if (\n    websocket._receiver._writableState.finished ||\n    websocket._receiver._writableState.errorEmitted\n  ) {\n    websocket.emitClose();\n  } else {\n    websocket._receiver.on('error', receiverOnFinish);\n    websocket._receiver.on('finish', receiverOnFinish);\n  }\n}\n\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction socketOnData(chunk) {\n  if (!this[kWebSocket]._receiver.write(chunk)) {\n    this.pause();\n  }\n}\n\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */\nfunction socketOnEnd() {\n  const websocket = this[kWebSocket];\n\n  websocket._readyState = WebSocket.CLOSING;\n  websocket._receiver.end();\n  this.end();\n}\n\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */\nfunction socketOnError() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('error', socketOnError);\n  this.on('error', NOOP);\n\n  if (websocket) {\n    websocket._readyState = WebSocket.CLOSING;\n    this.destroy();\n  }\n}\n"], "names": [], "mappings": "AAAA,0GAA0G,GAI1G,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE;AACjC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC1B,MAAM,EAAE,GAAG,EAAE;AAEb,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,IAAI,EACJ,oBAAoB,EACpB,SAAS,EACT,WAAW,EACX,UAAU,EACV,IAAI,EACL;AACD,MAAM,EACJ,aAAa,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,EACvD;AACD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;AACvB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM,eAAe,KAAK;AAC1B,MAAM,WAAW,OAAO;AACxB,MAAM,mBAAmB;IAAC;IAAG;CAAG;AAChC,MAAM,cAAc;IAAC;IAAc;IAAQ;IAAW;CAAS;AAC/D,MAAM,mBAAmB;AAEzB;;;;CAIC,GACD,MAAM,kBAAkB;IACtB;;;;;;GAMC,GACD,YAAY,OAAO,EAAE,SAAS,EAAE,OAAO,CAAE;QACvC,KAAK;QAEL,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE;QAClC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG,UAAU,UAAU;QACvC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,YAAY,MAAM;YACpB,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,UAAU,GAAG;YAElB,IAAI,cAAc,WAAW;gBAC3B,YAAY,EAAE;YAChB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;gBACpC,IAAI,OAAO,cAAc,YAAY,cAAc,MAAM;oBACvD,UAAU;oBACV,YAAY,EAAE;gBAChB,OAAO;oBACL,YAAY;wBAAC;qBAAU;gBACzB;YACF;YAEA,aAAa,IAAI,EAAE,SAAS,WAAW;QACzC,OAAO;YACL,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;YACjC,IAAI,CAAC,SAAS,GAAG;QACnB;IACF;IAEA;;;;;;GAMC,GACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,IAAI,WAAW,IAAI,EAAE;QACnB,IAAI,CAAC,aAAa,QAAQ,CAAC,OAAO;QAElC,IAAI,CAAC,WAAW,GAAG;QAEnB,EAAE;QACF,2CAA2C;QAC3C,EAAE;QACF,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG;IACnD;IAEA;;GAEC,GACD,IAAI,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,eAAe;QAE9C,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;IACzE;IAEA;;GAEC,GACD,IAAI,aAAa;QACf,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI;IAC3C;IAEA;;GAEC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,UAAU;QACZ,OAAO;IACT;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,UAAU;QACZ,OAAO;IACT;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,SAAS;QACX,OAAO;IACT;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,YAAY;QACd,OAAO;IACT;IAEA;;GAEC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;GAEC,GACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;GAEC,GACD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA;;;;;;;;;;;;;;;GAeC,GACD,UAAU,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;QAC/B,MAAM,WAAW,IAAI,SAAS;YAC5B,wBAAwB,QAAQ,sBAAsB;YACtD,YAAY,IAAI,CAAC,UAAU;YAC3B,YAAY,IAAI,CAAC,WAAW;YAC5B,UAAU,IAAI,CAAC,SAAS;YACxB,YAAY,QAAQ,UAAU;YAC9B,oBAAoB,QAAQ,kBAAkB;QAChD;QAEA,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,QAAQ,IAAI,CAAC,WAAW,EAAE,QAAQ,YAAY;QACxE,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QAEf,QAAQ,CAAC,WAAW,GAAG,IAAI;QAC3B,MAAM,CAAC,WAAW,GAAG,IAAI;QAEzB,SAAS,EAAE,CAAC,YAAY;QACxB,SAAS,EAAE,CAAC,SAAS;QACrB,SAAS,EAAE,CAAC,SAAS;QACrB,SAAS,EAAE,CAAC,WAAW;QACvB,SAAS,EAAE,CAAC,QAAQ;QACpB,SAAS,EAAE,CAAC,QAAQ;QAEpB,EAAE;QACF,qEAAqE;QACrE,EAAE;QACF,IAAI,OAAO,UAAU,EAAE,OAAO,UAAU,CAAC;QACzC,IAAI,OAAO,UAAU,EAAE,OAAO,UAAU;QAExC,IAAI,KAAK,MAAM,GAAG,GAAG,OAAO,OAAO,CAAC;QAEpC,OAAO,EAAE,CAAC,SAAS;QACnB,OAAO,EAAE,CAAC,QAAQ;QAClB,OAAO,EAAE,CAAC,OAAO;QACjB,OAAO,EAAE,CAAC,SAAS;QAEnB,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI;QACjC,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA;;;;GAIC,GACD,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,WAAW,GAAG,UAAU,MAAM;YACnC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa;YACtD;QACF;QAEA,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,EAAE;YACrD,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,CAAC,OAAO;QAC3D;QAEA,IAAI,CAAC,SAAS,CAAC,kBAAkB;QACjC,IAAI,CAAC,WAAW,GAAG,UAAU,MAAM;QACnC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa;IACxD;IAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,MAAM,IAAI,EAAE,IAAI,EAAE;QAChB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EAAE;QAC1C,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,MAAM;YACZ,eAAe,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YAChC;QACF;QAEA,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,OAAO,EAAE;YACzC,IACE,IAAI,CAAC,eAAe,IACpB,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,GACvE;gBACA,IAAI,CAAC,OAAO,CAAC,GAAG;YAClB;YAEA;QACF;QAEA,IAAI,CAAC,WAAW,GAAG,UAAU,OAAO;QACpC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/C,EAAE;YACF,yEAAyE;YACzE,sDAAsD;YACtD,EAAE;YACF,IAAI,KAAK;YAET,IAAI,CAAC,eAAe,GAAG;YAEvB,IACE,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,EAC1C;gBACA,IAAI,CAAC,OAAO,CAAC,GAAG;YAClB;QACF;QAEA,EAAE;QACF,2DAA2D;QAC3D,EAAE;QACF,IAAI,CAAC,WAAW,GAAG,WACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GACtC;IAEJ;IAEA;;;;GAIC,GACD,QAAQ;QACN,IACE,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,IACxC,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EACpC;YACA;QACF;QAEA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,CAAC,KAAK;IACpB;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,SAAS,YAAY;YAC9B,KAAK;YACL,OAAO,OAAO;QAChB,OAAO,IAAI,OAAO,SAAS,YAAY;YACrC,KAAK;YACL,OAAO;QACT;QAEA,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,QAAQ;QAElD,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACtC,eAAe,IAAI,EAAE,MAAM;YAC3B;QACF;QAEA,IAAI,SAAS,WAAW,OAAO,CAAC,IAAI,CAAC,SAAS;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,cAAc,MAAM;IAChD;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,SAAS,YAAY;YAC9B,KAAK;YACL,OAAO,OAAO;QAChB,OAAO,IAAI,OAAO,SAAS,YAAY;YACrC,KAAK;YACL,OAAO;QACT;QAEA,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,QAAQ;QAElD,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACtC,eAAe,IAAI,EAAE,MAAM;YAC3B;QACF;QAEA,IAAI,SAAS,WAAW,OAAO,CAAC,IAAI,CAAC,SAAS;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,cAAc,MAAM;IAChD;IAEA;;;;GAIC,GACD,SAAS;QACP,IACE,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,IACxC,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EACpC;YACA;QACF;QAEA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;IACnE;IAEA;;;;;;;;;;;;;;GAcC,GACD,KAAK,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACtB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,YAAY,YAAY;YACjC,KAAK;YACL,UAAU,CAAC;QACb;QAEA,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,QAAQ;QAElD,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACtC,eAAe,IAAI,EAAE,MAAM;YAC3B;QACF;QAEA,MAAM,OAAO;YACX,QAAQ,OAAO,SAAS;YACxB,MAAM,CAAC,IAAI,CAAC,SAAS;YACrB,UAAU;YACV,KAAK;YACL,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,EAAE;YACtD,KAAK,QAAQ,GAAG;QAClB;QAEA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,cAAc,MAAM;IAChD;IAEA;;;;GAIC,GACD,YAAY;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EAAE;QAC1C,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,MAAM;YACZ,eAAe,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YAChC;QACF;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,WAAW,GAAG,UAAU,OAAO;YACpC,IAAI,CAAC,OAAO,CAAC,OAAO;QACtB;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,cAAc;IAC7C,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,cAAc;IACvD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,QAAQ;IACvC,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,QAAQ;IACjD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,WAAW;IAC1C,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,WAAW;IACpD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,UAAU;IACzC,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,UAAU;IACnD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;IACE;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,OAAO,CAAC,CAAC;IACT,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,UAAU;QAAE,YAAY;IAAK;AAC1E;AAEA,EAAE;AACF,sEAAsE;AACtE,gFAAgF;AAChF,EAAE;AACF;IAAC;IAAQ;IAAS;IAAS;CAAU,CAAC,OAAO,CAAC,CAAC;IAC7C,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE;QACxD,YAAY;QACZ;YACE,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,QAAS;gBAC7C,IAAI,QAAQ,CAAC,qBAAqB,EAAE,OAAO,QAAQ,CAAC,UAAU;YAChE;YAEA,OAAO;QACT;QACA,KAAI,OAAO;YACT,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,QAAS;gBAC7C,IAAI,QAAQ,CAAC,qBAAqB,EAAE;oBAClC,IAAI,CAAC,cAAc,CAAC,QAAQ;oBAC5B;gBACF;YACF;YAEA,IAAI,OAAO,YAAY,YAAY;YAEnC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,SAAS;gBACrC,CAAC,qBAAqB,EAAE;YAC1B;QACF;IACF;AACF;AAEA,UAAU,SAAS,CAAC,gBAAgB,GAAG;AACvC,UAAU,SAAS,CAAC,mBAAmB,GAAG;AAE1C,OAAO,OAAO,GAAG;AAEjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GACD,SAAS,aAAa,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO;IAC1D,MAAM,OAAO;QACX,wBAAwB;QACxB,UAAU;QACV,iBAAiB,gBAAgB,CAAC,EAAE;QACpC,YAAY,MAAM,OAAO;QACzB,oBAAoB;QACpB,mBAAmB;QACnB,iBAAiB;QACjB,cAAc;QACd,GAAG,OAAO;QACV,YAAY;QACZ,UAAU;QACV,UAAU;QACV,SAAS;QACT,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,UAAU,SAAS,GAAG,KAAK,QAAQ;IAEnC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,eAAe,GAAG;QACpD,MAAM,IAAI,WACR,CAAC,8BAA8B,EAAE,KAAK,eAAe,CAAC,CAAC,CAAC,GACtD,CAAC,qBAAqB,EAAE,iBAAiB,IAAI,CAAC,MAAM,CAAC,CAAC;IAE5D;IAEA,IAAI;IAEJ,IAAI,mBAAmB,KAAK;QAC1B,YAAY;IACd,OAAO;QACL,IAAI;YACF,YAAY,IAAI,IAAI;QACtB,EAAE,OAAO,GAAG;YACV,MAAM,IAAI,YAAY,CAAC,aAAa,EAAE,SAAS;QACjD;IACF;IAEA,IAAI,UAAU,QAAQ,KAAK,SAAS;QAClC,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,UAAU,QAAQ,KAAK,UAAU;QAC1C,UAAU,QAAQ,GAAG;IACvB;IAEA,UAAU,IAAI,GAAG,UAAU,IAAI;IAE/B,MAAM,WAAW,UAAU,QAAQ,KAAK;IACxC,MAAM,WAAW,UAAU,QAAQ,KAAK;IACxC,IAAI;IAEJ,IAAI,UAAU,QAAQ,KAAK,SAAS,CAAC,YAAY,CAAC,UAAU;QAC1D,oBACE,uDACA;IACJ,OAAO,IAAI,YAAY,CAAC,UAAU,QAAQ,EAAE;QAC1C,oBAAoB;IACtB,OAAO,IAAI,UAAU,IAAI,EAAE;QACzB,oBAAoB;IACtB;IAEA,IAAI,mBAAmB;QACrB,MAAM,MAAM,IAAI,YAAY;QAE5B,IAAI,UAAU,UAAU,KAAK,GAAG;YAC9B,MAAM;QACR,OAAO;YACL,kBAAkB,WAAW;YAC7B;QACF;IACF;IAEA,MAAM,cAAc,WAAW,MAAM;IACrC,MAAM,MAAM,YAAY,IAAI,QAAQ,CAAC;IACrC,MAAM,UAAU,WAAW,MAAM,OAAO,GAAG,KAAK,OAAO;IACvD,MAAM,cAAc,IAAI;IACxB,IAAI;IAEJ,KAAK,gBAAgB,GACnB,KAAK,gBAAgB,IAAI,CAAC,WAAW,aAAa,UAAU;IAC9D,KAAK,WAAW,GAAG,KAAK,WAAW,IAAI;IACvC,KAAK,IAAI,GAAG,UAAU,IAAI,IAAI;IAC9B,KAAK,IAAI,GAAG,UAAU,QAAQ,CAAC,UAAU,CAAC,OACtC,UAAU,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAC7B,UAAU,QAAQ;IACtB,KAAK,OAAO,GAAG;QACb,GAAG,KAAK,OAAO;QACf,yBAAyB,KAAK,eAAe;QAC7C,qBAAqB;QACrB,YAAY;QACZ,SAAS;IACX;IACA,KAAK,IAAI,GAAG,UAAU,QAAQ,GAAG,UAAU,MAAM;IACjD,KAAK,OAAO,GAAG,KAAK,gBAAgB;IAEpC,IAAI,KAAK,iBAAiB,EAAE;QAC1B,oBAAoB,IAAI,kBACtB,KAAK,iBAAiB,KAAK,OAAO,KAAK,iBAAiB,GAAG,CAAC,GAC5D,OACA,KAAK,UAAU;QAEjB,KAAK,OAAO,CAAC,2BAA2B,GAAG,OAAO;YAChD,CAAC,kBAAkB,aAAa,CAAC,EAAE,kBAAkB,KAAK;QAC5D;IACF;IACA,IAAI,UAAU,MAAM,EAAE;QACpB,KAAK,MAAM,YAAY,UAAW;YAChC,IACE,OAAO,aAAa,YACpB,CAAC,iBAAiB,IAAI,CAAC,aACvB,YAAY,GAAG,CAAC,WAChB;gBACA,MAAM,IAAI,YACR;YAEJ;YAEA,YAAY,GAAG,CAAC;QAClB;QAEA,KAAK,OAAO,CAAC,yBAAyB,GAAG,UAAU,IAAI,CAAC;IAC1D;IACA,IAAI,KAAK,MAAM,EAAE;QACf,IAAI,KAAK,eAAe,GAAG,IAAI;YAC7B,KAAK,OAAO,CAAC,uBAAuB,GAAG,KAAK,MAAM;QACpD,OAAO;YACL,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,MAAM;QACnC;IACF;IACA,IAAI,UAAU,QAAQ,IAAI,UAAU,QAAQ,EAAE;QAC5C,KAAK,IAAI,GAAG,GAAG,UAAU,QAAQ,CAAC,CAAC,EAAE,UAAU,QAAQ,EAAE;IAC3D;IAEA,IAAI,UAAU;QACZ,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC;QAE9B,KAAK,UAAU,GAAG,KAAK,CAAC,EAAE;QAC1B,KAAK,IAAI,GAAG,KAAK,CAAC,EAAE;IACtB;IAEA,IAAI;IAEJ,IAAI,KAAK,eAAe,EAAE;QACxB,IAAI,UAAU,UAAU,KAAK,GAAG;YAC9B,UAAU,YAAY,GAAG;YACzB,UAAU,eAAe,GAAG;YAC5B,UAAU,yBAAyB,GAAG,WAClC,KAAK,UAAU,GACf,UAAU,IAAI;YAElB,MAAM,UAAU,WAAW,QAAQ,OAAO;YAE1C,EAAE;YACF,wEAAwE;YACxE,wCAAwC;YACxC,EAAE;YACF,UAAU;gBAAE,GAAG,OAAO;gBAAE,SAAS,CAAC;YAAE;YAEpC,IAAI,SAAS;gBACX,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;oBAClD,QAAQ,OAAO,CAAC,IAAI,WAAW,GAAG,GAAG;gBACvC;YACF;QACF,OAAO,IAAI,UAAU,aAAa,CAAC,gBAAgB,GAAG;YACpD,MAAM,aAAa,WACf,UAAU,YAAY,GACpB,KAAK,UAAU,KAAK,UAAU,yBAAyB,GACvD,QACF,UAAU,YAAY,GACpB,QACA,UAAU,IAAI,KAAK,UAAU,yBAAyB;YAE5D,IAAI,CAAC,cAAe,UAAU,eAAe,IAAI,CAAC,UAAW;gBAC3D,EAAE;gBACF,mEAAmE;gBACnE,qEAAqE;gBACrE,EAAE;gBACF,OAAO,KAAK,OAAO,CAAC,aAAa;gBACjC,OAAO,KAAK,OAAO,CAAC,MAAM;gBAE1B,IAAI,CAAC,YAAY,OAAO,KAAK,OAAO,CAAC,IAAI;gBAEzC,KAAK,IAAI,GAAG;YACd;QACF;QAEA,EAAE;QACF,4EAA4E;QAC5E,0EAA0E;QAC1E,wBAAwB;QACxB,EAAE;QACF,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,OAAO,CAAC,aAAa,EAAE;YAC/C,QAAQ,OAAO,CAAC,aAAa,GAC3B,WAAW,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,QAAQ,CAAC;QAC/C;QAEA,MAAM,UAAU,IAAI,GAAG,QAAQ;QAE/B,IAAI,UAAU,UAAU,EAAE;YACxB,EAAE;YACF,kEAAkE;YAClE,0DAA0D;YAC1D,0EAA0E;YAC1E,sEAAsE;YACtE,yEAAyE;YACzE,yEAAyE;YACzE,sCAAsC;YACtC,EAAE;YACF,UAAU,IAAI,CAAC,YAAY,UAAU,GAAG,EAAE;QAC5C;IACF,OAAO;QACL,MAAM,UAAU,IAAI,GAAG,QAAQ;IACjC;IAEA,IAAI,KAAK,OAAO,EAAE;QAChB,IAAI,EAAE,CAAC,WAAW;YAChB,eAAe,WAAW,KAAK;QACjC;IACF;IAEA,IAAI,EAAE,CAAC,SAAS,CAAC;QACf,IAAI,QAAQ,QAAQ,GAAG,CAAC,SAAS,EAAE;QAEnC,MAAM,UAAU,IAAI,GAAG;QACvB,kBAAkB,WAAW;IAC/B;IAEA,IAAI,EAAE,CAAC,YAAY,CAAC;QAClB,MAAM,WAAW,IAAI,OAAO,CAAC,QAAQ;QACrC,MAAM,aAAa,IAAI,UAAU;QAEjC,IACE,YACA,KAAK,eAAe,IACpB,cAAc,OACd,aAAa,KACb;YACA,IAAI,EAAE,UAAU,UAAU,GAAG,KAAK,YAAY,EAAE;gBAC9C,eAAe,WAAW,KAAK;gBAC/B;YACF;YAEA,IAAI,KAAK;YAET,IAAI;YAEJ,IAAI;gBACF,OAAO,IAAI,IAAI,UAAU;YAC3B,EAAE,OAAO,GAAG;gBACV,MAAM,MAAM,IAAI,YAAY,CAAC,aAAa,EAAE,UAAU;gBACtD,kBAAkB,WAAW;gBAC7B;YACF;YAEA,aAAa,WAAW,MAAM,WAAW;QAC3C,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,uBAAuB,KAAK,MAAM;YAC3D,eACE,WACA,KACA,CAAC,4BAA4B,EAAE,IAAI,UAAU,EAAE;QAEnD;IACF;IAEA,IAAI,EAAE,CAAC,WAAW,CAAC,KAAK,QAAQ;QAC9B,UAAU,IAAI,CAAC,WAAW;QAE1B,EAAE;QACF,iEAAiE;QACjE,qBAAqB;QACrB,EAAE;QACF,IAAI,UAAU,UAAU,KAAK,UAAU,UAAU,EAAE;QAEnD,MAAM,UAAU,IAAI,GAAG;QAEvB,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO;QAEnC,IAAI,YAAY,aAAa,QAAQ,WAAW,OAAO,aAAa;YAClE,eAAe,WAAW,QAAQ;YAClC;QACF;QAEA,MAAM,SAAS,WAAW,QACvB,MAAM,CAAC,MAAM,MACb,MAAM,CAAC;QAEV,IAAI,IAAI,OAAO,CAAC,uBAAuB,KAAK,QAAQ;YAClD,eAAe,WAAW,QAAQ;YAClC;QACF;QAEA,MAAM,aAAa,IAAI,OAAO,CAAC,yBAAyB;QACxD,IAAI;QAEJ,IAAI,eAAe,WAAW;YAC5B,IAAI,CAAC,YAAY,IAAI,EAAE;gBACrB,YAAY;YACd,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,aAAa;gBACvC,YAAY;YACd;QACF,OAAO,IAAI,YAAY,IAAI,EAAE;YAC3B,YAAY;QACd;QAEA,IAAI,WAAW;YACb,eAAe,WAAW,QAAQ;YAClC;QACF;QAEA,IAAI,YAAY,UAAU,SAAS,GAAG;QAEtC,MAAM,yBAAyB,IAAI,OAAO,CAAC,2BAA2B;QAEtE,IAAI,2BAA2B,WAAW;YACxC,IAAI,CAAC,mBAAmB;gBACtB,MAAM,UACJ,oEACA;gBACF,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,IAAI;YAEJ,IAAI;gBACF,aAAa,MAAM;YACrB,EAAE,OAAO,KAAK;gBACZ,MAAM,UAAU;gBAChB,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,MAAM,iBAAiB,OAAO,IAAI,CAAC;YAEnC,IACE,eAAe,MAAM,KAAK,KAC1B,cAAc,CAAC,EAAE,KAAK,kBAAkB,aAAa,EACrD;gBACA,MAAM,UAAU;gBAChB,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,IAAI;gBACF,kBAAkB,MAAM,CAAC,UAAU,CAAC,kBAAkB,aAAa,CAAC;YACtE,EAAE,OAAO,KAAK;gBACZ,MAAM,UAAU;gBAChB,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,UAAU,WAAW,CAAC,kBAAkB,aAAa,CAAC,GACpD;QACJ;QAEA,UAAU,SAAS,CAAC,QAAQ,MAAM;YAChC,wBAAwB,KAAK,sBAAsB;YACnD,cAAc,KAAK,YAAY;YAC/B,YAAY,KAAK,UAAU;YAC3B,oBAAoB,KAAK,kBAAkB;QAC7C;IACF;IAEA,IAAI,KAAK,aAAa,EAAE;QACtB,KAAK,aAAa,CAAC,KAAK;IAC1B,OAAO;QACL,IAAI,GAAG;IACT;AACF;AAEA;;;;;;CAMC,GACD,SAAS,kBAAkB,SAAS,EAAE,GAAG;IACvC,UAAU,WAAW,GAAG,UAAU,OAAO;IACzC,UAAU,IAAI,CAAC,SAAS;IACxB,UAAU,SAAS;AACrB;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,OAAO;IACzB,QAAQ,IAAI,GAAG,QAAQ,UAAU;IACjC,OAAO,IAAI,OAAO,CAAC;AACrB;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,OAAO;IACzB,QAAQ,IAAI,GAAG;IAEf,IAAI,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,IAAI;QACpD,QAAQ,UAAU,GAAG,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI;IACjE;IAEA,OAAO,IAAI,OAAO,CAAC;AACrB;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,SAAS,EAAE,MAAM,EAAE,OAAO;IAChD,UAAU,WAAW,GAAG,UAAU,OAAO;IAEzC,MAAM,MAAM,IAAI,MAAM;IACtB,MAAM,iBAAiB,CAAC,KAAK;IAE7B,IAAI,OAAO,SAAS,EAAE;QACpB,MAAM,CAAC,SAAS,GAAG;QACnB,OAAO,KAAK;QAEZ,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,SAAS,EAAE;YAC7C,EAAE;YACF,wEAAwE;YACxE,0CAA0C;YAC1C,gDAAgD;YAChD,EAAE;YACF,OAAO,MAAM,CAAC,OAAO;QACvB;QAEA,QAAQ,QAAQ,CAAC,mBAAmB,WAAW;IACjD,OAAO;QACL,OAAO,OAAO,CAAC;QACf,OAAO,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW;QACpD,OAAO,IAAI,CAAC,SAAS,UAAU,SAAS,CAAC,IAAI,CAAC;IAChD;AACF;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,SAAS,EAAE,IAAI,EAAE,EAAE;IACzC,IAAI,MAAM;QACR,MAAM,SAAS,SAAS,MAAM,MAAM;QAEpC,EAAE;QACF,4EAA4E;QAC5E,uEAAuE;QACvE,qEAAqE;QACrE,gCAAgC;QAChC,EAAE;QACF,IAAI,UAAU,OAAO,EAAE,UAAU,OAAO,CAAC,cAAc,IAAI;aACtD,UAAU,eAAe,IAAI;IACpC;IAEA,IAAI,IAAI;QACN,MAAM,MAAM,IAAI,MACd,CAAC,kCAAkC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC,GAC1D,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,UAAU,CAAC,CAAC,CAAC,CAAC;QAE5C,QAAQ,QAAQ,CAAC,IAAI;IACvB;AACF;AAEA;;;;;;CAMC,GACD,SAAS,mBAAmB,IAAI,EAAE,MAAM;IACtC,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,UAAU,mBAAmB,GAAG;IAChC,UAAU,aAAa,GAAG;IAC1B,UAAU,UAAU,GAAG;IAEvB,IAAI,UAAU,OAAO,CAAC,WAAW,KAAK,WAAW;IAEjD,UAAU,OAAO,CAAC,cAAc,CAAC,QAAQ;IACzC,QAAQ,QAAQ,CAAC,QAAQ,UAAU,OAAO;IAE1C,IAAI,SAAS,MAAM,UAAU,KAAK;SAC7B,UAAU,KAAK,CAAC,MAAM;AAC7B;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,CAAC,UAAU,QAAQ,EAAE,UAAU,OAAO,CAAC,MAAM;AACnD;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,GAAG;IAC1B,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,UAAU,OAAO,CAAC,WAAW,KAAK,WAAW;QAC/C,UAAU,OAAO,CAAC,cAAc,CAAC,QAAQ;QAEzC,EAAE;QACF,wEAAwE;QACxE,gDAAgD;QAChD,EAAE;QACF,QAAQ,QAAQ,CAAC,QAAQ,UAAU,OAAO;QAE1C,UAAU,KAAK,CAAC,GAAG,CAAC,YAAY;IAClC;IAEA,UAAU,IAAI,CAAC,SAAS;AAC1B;AAEA;;;;CAIC,GACD,SAAS;IACP,IAAI,CAAC,WAAW,CAAC,SAAS;AAC5B;AAEA;;;;;;CAMC,GACD,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACvC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,MAAM;AACzC;AAEA;;;;;CAKC,GACD,SAAS,eAAe,IAAI;IAC1B,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,UAAU,SAAS,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;IAC/D,UAAU,IAAI,CAAC,QAAQ;AACzB;AAEA;;;;;CAKC,GACD,SAAS,eAAe,IAAI;IAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ;AAChC;AAEA;;;;;CAKC,GACD,SAAS,OAAO,MAAM;IACpB,OAAO,MAAM;AACf;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7B,IAAI,CAAC,cAAc,CAAC,QAAQ;IAC5B,IAAI,CAAC,cAAc,CAAC,OAAO;IAE3B,UAAU,WAAW,GAAG,UAAU,OAAO;IAEzC,IAAI;IAEJ,EAAE;IACF,6EAA6E;IAC7E,4EAA4E;IAC5E,2EAA2E;IAC3E,6EAA6E;IAC7E,6EAA6E;IAC7E,8EAA8E;IAC9E,uCAAuC;IACvC,EAAE;IACF,IACE,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,IAC/B,CAAC,UAAU,mBAAmB,IAC9B,CAAC,UAAU,SAAS,CAAC,cAAc,CAAC,YAAY,IAChD,CAAC,QAAQ,UAAU,OAAO,CAAC,IAAI,EAAE,MAAM,MACvC;QACA,UAAU,SAAS,CAAC,KAAK,CAAC;IAC5B;IAEA,UAAU,SAAS,CAAC,GAAG;IAEvB,IAAI,CAAC,WAAW,GAAG;IAEnB,aAAa,UAAU,WAAW;IAElC,IACE,UAAU,SAAS,CAAC,cAAc,CAAC,QAAQ,IAC3C,UAAU,SAAS,CAAC,cAAc,CAAC,YAAY,EAC/C;QACA,UAAU,SAAS;IACrB,OAAO;QACL,UAAU,SAAS,CAAC,EAAE,CAAC,SAAS;QAChC,UAAU,SAAS,CAAC,EAAE,CAAC,UAAU;IACnC;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK;IACzB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ;QAC5C,IAAI,CAAC,KAAK;IACZ;AACF;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,UAAU,WAAW,GAAG,UAAU,OAAO;IACzC,UAAU,SAAS,CAAC,GAAG;IACvB,IAAI,CAAC,GAAG;AACV;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7B,IAAI,CAAC,EAAE,CAAC,SAAS;IAEjB,IAAI,WAAW;QACb,UAAU,WAAW,GAAG,UAAU,OAAO;QACzC,IAAI,CAAC,OAAO;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5965, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/aes.js", "sourceRoot": "", "sources": ["../src.ts/aes.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,qFAAA,EAAuF;;;;;;;;;;;;;;;AAEvF,8BAA8B;AAC9B,MAAM,cAAc,GAA2B;IAAE,EAAE,EAAE,EAAE;IAAE,EAAE,EAAE,EAAE;IAAE,EAAE,EAAE,EAAE;AAAA,CAAE,CAAC;AAE1E,uBAAuB;AACvB,MAAM,IAAI,GAAG;IAAC,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;CAAC,CAAC;AAElM,kDAAkD;AAClD,MAAM,CAAC,GAAG;IAAC,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;CAAC,CAAC;AAC3gD,MAAM,EAAE,GAAE;IAAC,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;IAAE,IAAI;CAAC,CAAC;AAE3gD,iCAAiC;AACjC,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAE5gG,iCAAiC;AACjC,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAE5gG,+CAA+C;AAC/C,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAC5gG,MAAM,EAAE,GAAG;IAAC,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;IAAE,UAAU;CAAC,CAAC;AAE5gG,SAAS,cAAc,CAAC,KAAiB;IACvC,MAAM,MAAM,GAAG,EAAG,CAAC;IACnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAE;QACxC,MAAM,CAAC,IAAI,CAAC,AAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAI,CAAD,IAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAI,CAAD,IAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC,EAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC5F;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,MAAO,GAAG;IAKd,IAAI,GAAG,GAAA;QAAiB,OAAO,uBAAA,IAAI,EAAA,UAAA,IAAK,CAAC,KAAK,EAAE,CAAC;IAAC,CAAC;IAEnD,YAAY,GAAe,CAAA;QAN3B,SAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAA0B;QAC1B,QAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAmC;QACnC,QAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAmC;QAKjC,IAAI,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;YAC1B,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACpD;QAED,uBAAA,IAAI,EAAA,UAAQ,IAAI,UAAU,CAAC,GAAG,CAAC,EAAA,IAAA,CAAC;QAEhC,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,MAAM,IAAI,SAAS,CAAC,+CAA+C,CAAC,CAAC;SACtE;QAED,wBAAwB;QACxB,uBAAA,IAAI,EAAA,SAAO,EAAE,EAAA,IAAA,CAAC;QAEd,wBAAwB;QACxB,uBAAA,IAAI,EAAA,SAAO,EAAE,EAAA,IAAA,CAAC;QAEd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,EAAE,CAAE;YAChC,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,IAAI,CAAC;gBAAC,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;aAAC,CAAC,CAAC;YAC5B,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,IAAI,CAAC;gBAAC,CAAC;gBAAE,CAAC;gBAAE,CAAC;gBAAE,CAAC;aAAC,CAAC,CAAC;SAC7B;QAED,MAAM,aAAa,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QAE/B,4BAA4B;QAC5B,MAAM,EAAE,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEpC,oCAAoC;QACpC,IAAI,KAAK,CAAC;QACV,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YAC3B,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;YACf,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/B,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;SACzC;QAED,uCAAuC;QACvC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC;QACf,MAAO,CAAC,GAAG,aAAa,CAAE;YACxB,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YAChB,EAAE,CAAC,CAAC,CAAC,IAAI,AAAC,AAAC,CAAC,CAAC,AAAC,EAAE,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAC3B,CAAC,CAAC,AAAC,EAAE,IAAK,CAAC,CAAC,EAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAC3B,CAAC,CAAE,EAAE,GAAU,IAAI,CAAC,IAAK,CAAC,CAAC,EAC3B,CAAC,CAAC,AAAC,EAAE,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GACpB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACrC,WAAW,IAAI,CAAC,CAAC;YAEjB,kCAAkC;YAClC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACX,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;oBAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACpB;YAEH,oEAAoE;aACnE,MAAM;gBACL,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,AAAC,EAAE,GAAG,CAAC,CAAC,CAAE,CAAC,EAAE,CAAE;oBACjC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACpB;gBACD,EAAE,GAAG,EAAE,CAAC,AAAC,EAAE,GAAG,CAAC,CAAC,EAAG,CAAC,CAAC,CAAC;gBAEtB,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,AAAC,CAAC,CAAE,EAAE,GAAU,IAAI,CAAC,GACpB,CAAC,CAAC,AAAC,EAAE,IAAK,CAAC,CAAC,EAAG,IAAI,CAAC,IAAK,CAAC,CAAC,EAC3B,CAAC,CAAC,AAAC,EAAE,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAC3B,CAAC,CAAC,AAAC,EAAE,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAE5C,IAAK,IAAI,CAAC,GAAG,AAAC,EAAE,GAAG,CAAC,CAAC,EAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;oBACtC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACpB;aACF;YAED,oCAAoC;YACpC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAChB,MAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,aAAa,CAAE;gBAClC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACX,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACV,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvB,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBAClC,CAAC,EAAE,CAAC;aACL;SACF;QAED,qEAAqE;QACrE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE;YAC/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBAC1B,EAAE,GAAG,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,EAAE,CAAC,AAAC,EAAE,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GACrB,EAAE,CAAE,AAAD,EAAG,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GACrB,EAAE,CAAC,AAAC,EAAE,IAAK,CAAC,CAAC,EAAG,IAAI,CAAC,GACrB,EAAE,CAAE,EAAE,GAAU,IAAI,CAAC,CAAC,CAAC;aAC1C;SACF;IACH,CAAC;IAED,OAAO,CAAC,SAAqB,EAAA;QAC3B,IAAI,SAAS,CAAC,MAAM,IAAI,EAAE,EAAE;YAC1B,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAC;SAClE;QAED,MAAM,MAAM,GAAG,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAEvB,oCAAoC;QACpC,IAAI,CAAC,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAClC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAC1B,CAAC,CAAC,CAAC,CAAC,IAAI,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACxB;QAED,yBAAyB;QACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE;YAC/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBAC1B,CAAC,CAAC,CAAC,CAAC,GAAI,AAAD,EAAG,CAAC,AAAC,CAAC,CAAE,CAAC,CAAU,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GACjC,EAAE,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GACjC,EAAE,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC,EAAG,IAAI,CAAC,GACjC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAU,IAAI,CAAC,GACjC,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzB;YACD,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;SACf;QAED,4BAA4B;QAC5B,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAC1B,EAAE,GAAG,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAK,GAAG,CAAC,CAAC,CAAC,AAAC,CAAC,CAAE,CAAC,CAAU,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GAAG,AAAC,EAAE,IAAI,EAAE,AAAC,CAAC,GAAG,IAAI,CAAC;YAC3E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GAAG,AAAC,EAAE,IAAI,EAAE,AAAC,CAAC,GAAG,IAAI,CAAC;YAC3E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,AAAD,CAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC,EAAG,IAAI,CAAC,GAAG,AAAC,EAAE,IAAK,CAAC,AAAC,CAAC,GAAG,IAAI,CAAC;YAC3E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAU,IAAI,CAAC,GAAI,EAAE,CAAQ,GAAG,IAAI,CAAC;SAC5E;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,CAAC,UAAsB,EAAA;QAC5B,IAAI,UAAU,CAAC,MAAM,IAAI,EAAE,EAAE;YAC3B,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC,CAAC;SACnE;QAED,MAAM,MAAM,GAAG,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAEvB,oCAAoC;QACpC,IAAI,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;QACnC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAC1B,CAAC,CAAC,CAAC,CAAC,IAAI,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACxB;QAED,yBAAyB;QACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE;YAC/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBAC1B,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,EAAE,CAAC,AAAC,CAAC,CAAE,CAAC,CAAU,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GACjC,EAAE,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GACjC,EAAE,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC,EAAG,IAAI,CAAC,GACjC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAU,IAAI,CAAC,GACjC,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzB;YACD,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;SACf;QAED,4BAA4B;QAC5B,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAC1B,EAAE,GAAG,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAK,GAAG,CAAC,EAAE,CAAC,AAAC,CAAC,CAAE,CAAC,CAAU,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GAAG,AAAC,EAAE,IAAI,EAAE,AAAC,CAAC,GAAG,IAAI,CAAC;YAC5E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAG,IAAI,CAAC,GAAG,AAAC,EAAE,IAAI,EAAE,AAAC,CAAC,GAAG,IAAI,CAAC;YAC5E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC,EAAG,IAAI,CAAC,GAAG,AAAC,EAAE,IAAK,CAAE,AAAD,CAAE,GAAG,IAAI,CAAC;YAC5E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAU,IAAI,CAAC,GAAI,EAAE,CAAQ,GAAG,IAAI,CAAC;SAC7E;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF", "debugId": null}}, {"offset": {"line": 9804, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/mode.js", "sourceRoot": "", "sources": ["../src.ts/mode.ts"], "sourcesContent": [], "names": [], "mappings": ";;;AACA,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;;AAEzB,MAAgB,eAAe;IAInC,YAAY,IAAY,EAAE,GAAe,EAAE,GAAS,CAAA;QAClD,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,GAAI,IAAK,CAAA,gCAAA,CAAkC,CAAC,CAAC;SAC9D;QAED,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,GAAG,EAAE;gBAAE,UAAU,EAAE,IAAI;gBAAE,KAAK,EAAE,mJAAI,MAAG,CAAC,GAAG,CAAC;YAAA,CAAE;YAC9C,IAAI,EAAE;gBAAE,UAAU,EAAE,IAAI;gBAAE,KAAK,EAAE,IAAI;YAAA,CAAE;SACxC,CAAC,CAAC;IACL,CAAC;CAIF", "debugId": null}}, {"offset": {"line": 9830, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/mode-cbc.js", "sourceRoot": "", "sources": ["../src.ts/mode-cbc.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,wBAAwB;;;;AAExB,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;AAEtC,MAAO,GAAI,yJAAQ,kBAAe;IAItC,YAAY,GAAe,EAAE,EAAe,CAAA;QAC1C,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAJzB,QAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAgB;QAChB,eAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAuB;QAKrB,IAAI,EAAE,EAAE;YACN,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE;gBAClB,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;aAC3D;YACD,uBAAA,IAAI,EAAA,SAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,IAAA,CAAC;SAC/B,MAAM;YACL,uBAAA,IAAI,EAAA,SAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,IAAA,CAAC;SAC/B;QAED,uBAAA,IAAI,EAAA,gBAAc,IAAI,CAAC,EAAE,EAAA,IAAA,CAAC;IAC5B,CAAC;IAED,IAAI,EAAE,GAAA;QAAiB,OAAO,IAAI,UAAU,CAAC,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC;IAAC,CAAC;IAEzD,OAAO,CAAC,SAAqB,EAAA;QAC3B,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE;YACzB,MAAM,IAAI,SAAS,CAAC,uDAAuD,CAAC,CAAC;SAC9E;QAED,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAE;YAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBAC3B,uBAAA,IAAI,EAAA,gBAAA,IAAW,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACxC;YAED,uBAAA,IAAI,EAAA,gBAAc,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,EAAA,gBAAA,IAAW,CAAC,EAAA,IAAA,CAAC;YACpD,UAAU,CAAC,GAAG,CAAC,uBAAA,IAAI,EAAA,gBAAA,IAAW,EAAE,CAAC,CAAC,CAAC;SACpC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,UAAsB,EAAA;QAC5B,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE;YACxB,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;SACjF;QAED,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACpD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAE/D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBAC3B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,uBAAA,IAAI,EAAA,gBAAA,IAAW,CAAC,CAAC,CAAC,CAAC;gBACjD,uBAAA,IAAI,EAAA,gBAAA,IAAW,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACxC;SACJ;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "debugId": null}}, {"offset": {"line": 9900, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/mode-cfb.js", "sourceRoot": "", "sources": ["../src.ts/mode-cfb.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,kBAAkB;;;;AAElB,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;AAEtC,MAAO,GAAI,yJAAQ,kBAAe;IAMtC,YAAY,GAAe,EAAE,EAAe,EAAE,cAAsB,CAAC,CAAA;QACnE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;;QANzB,QAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAgB;QAChB,mBAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAA2B;QAOzB,+DAA+D;QAC/D,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,AAAC,WAAW,GAAG,CAAC,CAAC,CAAE;YACvD,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;SAC5C;QAED,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,WAAW,EAAE;gBAAE,UAAU,EAAE,IAAI;gBAAE,KAAK,EAAE,WAAW;YAAA,CAAE;SACtD,CAAC,CAAC;QAEH,IAAI,EAAE,EAAE;YACN,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE;gBAClB,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;aAC3D;YACD,uBAAA,IAAI,EAAA,SAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,IAAA,CAAC;SAC/B,MAAM;YACL,uBAAA,IAAI,EAAA,SAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,IAAA,CAAC;SAC/B;QAED,uBAAA,IAAI,EAAA,oBAAkB,IAAI,CAAC,EAAE,EAAA,IAAA,CAAC;IAChC,CAAC;IAED,IAAI,EAAE,GAAA;QAAiB,OAAO,IAAI,UAAU,CAAC,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC;IAAC,CAAC;IAUzD,OAAO,CAAC,SAAqB,EAAA;QAC3B,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YAC3C,MAAM,IAAI,SAAS,CAAC,gEAAgE,CAAC,CAAC;SACvF;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QAEzC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QAE7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW,CAAE;YACvD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,EAAA,oBAAA,IAAe,CAAC,CAAC;YACzD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE;gBACpC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;aACpC;YAED,uBAAA,IAAI,EAAA,gBAAA,KAAA,WAAO,CAAA,IAAA,CAAX,IAAI,EAAQ,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,UAAsB,EAAA;QAC5B,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YAC1C,MAAM,IAAI,SAAS,CAAC,iEAAiE,CAAC,CAAC;SAC1F;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QAEzC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAE7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW,CAAE;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,EAAA,oBAAA,IAAe,CAAC,CAAC;YACzD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE;gBACpC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;aACnC;YAED,uBAAA,IAAI,EAAA,gBAAA,KAAA,WAAO,CAAA,IAAA,CAAX,IAAI,EAAQ,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;8HAjDQ,IAAgB;IACrB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IAEzC,qBAAqB;IACrB,uBAAA,IAAI,EAAA,oBAAA,IAAe,CAAC,GAAG,CAAC,uBAAA,IAAI,EAAA,oBAAA,IAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;IACnE,uBAAA,IAAI,EAAA,oBAAA,IAAe,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,CAAC;AAC3E,CAAC", "debugId": null}}, {"offset": {"line": 9988, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/mode-ctr.js", "sourceRoot": "", "sources": ["../src.ts/mode-ctr.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,eAAe;;;;AAEf,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;AAEtC,MAAO,GAAI,yJAAQ,kBAAe;IAStC,YAAY,GAAe,EAAE,YAAkC,CAAA;QAC7D,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QARzB,uCAAuC;QACvC,eAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAuB;QACvB,oBAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAwB;QAExB,sBAAsB;QACtB,aAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAqB;QAKnB,uBAAA,IAAI,EAAA,cAAY,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,IAAA,CAAA;QAClC,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEtB,uBAAA,IAAI,EAAA,gBAAc,uBAAA,IAAI,EAAA,cAAA,IAAS,EAAA,IAAA,CAAC,CAAE,qCAAqC;QACvE,uBAAA,IAAI,EAAA,qBAAmB,EAAE,EAAA,IAAA,CAAC;QAE1B,IAAI,YAAY,IAAI,IAAI,EAAE;YAAE,YAAY,GAAG,CAAC,CAAC;SAAE;QAE/C,IAAI,OAAM,AAAC,YAAY,CAAC,IAAK,QAAQ,EAAE;YACrC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;SACpC,MAAM;YACL,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;SACpC;IACH,CAAC;IAED,IAAI,OAAO,GAAA;QAAiB,OAAO,IAAI,UAAU,CAAC,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,CAAC;IAAC,CAAC;IAEnE,eAAe,CAAC,KAAa,EAAA;QAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,gBAAgB,EAAE;YAC5E,MAAM,IAAI,SAAS,CAAC,uCAAuC,CAAC,CAAC;SAC9D;QAED,IAAK,IAAI,KAAK,GAAG,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,CAAE;YACxC,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC;YACnC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;SACjC;IACH,CAAC;IAED,eAAe,CAAC,KAAiB,EAAA;QAC/B,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;YACvB,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC;SACxE;QAED,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,SAAS,GAAA;QACP,IAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAE;YAC5B,IAAI,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC5B,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACtB,MAAM;gBACL,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnB,MAAM;aACP;SACF;IACH,CAAC;IAED,OAAO,CAAC,SAAqB,EAAA;;QAC3B,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QAE5C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YACzC,IAAI,uBAAA,IAAI,EAAA,qBAAA,IAAgB,KAAK,EAAE,EAAE;gBAC/B,uBAAA,IAAI,EAAA,gBAAc,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,EAAA,cAAA,IAAS,CAAC,EAAA,IAAA,CAAC;gBAClD,uBAAA,IAAI,EAAA,qBAAmB,CAAC,EAAA,IAAA,CAAC;gBACzB,IAAI,CAAC,SAAS,EAAE,CAAC;aAClB;YACD,SAAS,CAAC,CAAC,CAAC,IAAI,uBAAA,IAAI,EAAA,gBAAA,IAAW,CAAC,uBAAA,IAAA,EAAA,qBAAA,CAAA,KAAA,uBAAA,IAAA,EAAA,qBAAA,IAAoB,EAApB,KAAA,IAAsB,EAAA,EAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAC,CAAC;SACzD;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,CAAC,UAAsB,EAAA;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF", "debugId": null}}, {"offset": {"line": 10077, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/mode-ecb.js", "sourceRoot": "", "sources": ["../src.ts/mode-ecb.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,uBAAuB;;;;AAEvB,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;;AAEtC,MAAO,GAAI,yJAAQ,kBAAe;IAEtC,YAAY,GAAe,CAAA;QACzB,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACzB,CAAC;IAED,OAAO,CAAC,SAAqB,EAAA;QAC3B,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE;YACvB,MAAM,IAAI,SAAS,CAAC,uDAAuD,CAAC,CAAC;SAChF;QAED,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACnD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAE;YAC3C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACrE;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,CAAC,SAAqB,EAAA;QAC3B,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE;YACvB,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;SACjF;QAED,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACnD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAE;YAC3C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACrE;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "debugId": null}}, {"offset": {"line": 10112, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/mode-ofb.js", "sourceRoot": "", "sources": ["../src.ts/mode-ofb.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,kBAAkB;;;;AAElB,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;AAEtC,MAAO,GAAI,yJAAQ,kBAAe;IAKtC,YAAY,GAAe,EAAE,EAAe,CAAA;QAC1C,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QALzB,QAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAAgB;QAChB,mBAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAA2B;QAC3B,wBAAA,GAAA,CAAA,IAAA,EAAA,KAAA,GAA4B;QAK1B,IAAI,EAAE,EAAE;YACN,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE;gBAClB,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;aAC3D;YACD,uBAAA,IAAI,EAAA,SAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,IAAA,CAAC;SAC/B,MAAM;YACL,uBAAA,IAAI,EAAA,SAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,IAAA,CAAC;SAC/B;QAED,uBAAA,IAAI,EAAA,oBAAkB,IAAI,CAAC,EAAE,EAAA,IAAA,CAAC;QAC9B,uBAAA,IAAI,EAAA,yBAAuB,EAAE,EAAA,IAAA,CAAC;IAChC,CAAC;IAED,IAAI,EAAE,GAAA;QAAiB,OAAO,IAAI,UAAU,CAAC,uBAAA,IAAI,EAAA,SAAA,IAAI,CAAC,CAAC;IAAC,CAAC;IAEzD,OAAO,CAAC,SAAqB,EAAA;;QAC3B,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE;YACzB,MAAM,IAAI,SAAS,CAAC,uDAAuD,CAAC,CAAC;SAC9E;QAED,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YAC1C,IAAI,uBAAA,IAAI,EAAA,yBAAA,IAAoB,KAAK,EAAE,EAAE;gBACjC,uBAAA,IAAI,EAAA,oBAAkB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,EAAA,oBAAA,IAAe,CAAC,EAAA,IAAA,CAAC;gBAC5D,uBAAA,IAAI,EAAA,yBAAuB,CAAC,EAAA,IAAA,CAAC;aAChC;YACD,UAAU,CAAC,CAAC,CAAC,IAAI,uBAAA,IAAI,EAAA,oBAAA,IAAe,CAAC,uBAAA,IAAA,EAAA,yBAAA,CAAA,KAAA,uBAAA,IAAA,EAAA,yBAAA,IAAwB,EAAxB,KAAA,IAA0B,EAAA,EAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAC,CAAC;SAClE;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,UAAsB,EAAA;QAC5B,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE;YACxB,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;SACjF;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF", "debugId": null}}, {"offset": {"line": 10177, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/padding.js", "sourceRoot": "", "sources": ["../src.ts/padding.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AACM,SAAU,QAAQ,CAAC,IAAgB;IACrC,MAAM,MAAM,GAAG,EAAE,GAAG,AAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;IAEvC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IACpD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEjB,IAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QAC9C,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;KACtB;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAEK,SAAU,UAAU,CAAC,IAAgB;IACvC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE;QAAE,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;KAAE;IAEvE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrC,IAAI,MAAM,GAAG,EAAE,EAAE;QAAE,MAAM,IAAI,SAAS,CAAC,kCAAkC,CAAC,CAAC;KAAE;IAE7E,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACpC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE;QAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE;YAC7B,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAC;SACtD;KACJ;IAED,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACpD,CAAC", "debugId": null}}, {"offset": {"line": 10210, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/aes-js/lib.esm/index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "sourcesContent": [], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAE/B,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;AAE5C,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AACpC,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AACpC,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AACpC,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AACpC,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AAEpC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC", "debugId": null}}]}