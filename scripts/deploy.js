async function main() {
  console.log("DATACOIN start");
  const [deployer] = await ethers.getSigners();
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = await DATACOIN.deploy(deployer.address, {
  gasLimit: 200000,
  gasPrice: 366833801,

});
  await token.deployed();

  console.log("DATACOIN deployed to:", token.address);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
